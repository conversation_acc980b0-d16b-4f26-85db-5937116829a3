import { NextRequest, NextResponse } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { healthCheckSchema } from '@/lib/schemas'
import { prisma } from '@/lib/prisma'

// GET /api/health - Health check endpoint
export const GET = createAPIHandler(
  {},
  async (request: NextRequest) => {
    const checks = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      version: process.env.APP_VERSION || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: process.uptime(),
      checks: {
        database: 'unknown',
        memory: 'unknown',
        disk: 'unknown'
      }
    }

    try {
      // Database health check
      await prisma.$queryRaw`SELECT 1`
      checks.checks.database = 'healthy'
    } catch (error) {
      checks.checks.database = 'unhealthy'
      checks.status = 'degraded'
    }

    // Memory usage check
    const memoryUsage = process.memoryUsage()
    const memoryUsageMB = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024)
    }

    // Consider unhealthy if heap usage is over 500MB
    if (memoryUsageMB.heapUsed > 500) {
      checks.checks.memory = 'warning'
      if (checks.status === 'healthy') checks.status = 'degraded'
    } else {
      checks.checks.memory = 'healthy'
    }

    // Add memory details
    checks.checks = {
      ...checks.checks,
      memoryUsage: memoryUsageMB
    }

    const statusCode = checks.status === 'healthy' ? 200 : 
                      checks.status === 'degraded' ? 200 : 503

    return APIResponse.success(checks, `System is ${checks.status}`, statusCode)
  }
)

// HEAD /api/health - Lightweight health check
export const HEAD = createAPIHandler(
  {},
  async (request: NextRequest) => {
    try {
      // Quick database ping
      await prisma.$queryRaw`SELECT 1`
      return new NextResponse(null, { status: 200 })
    } catch (error) {
      return new NextResponse(null, { status: 503 })
    }
  }
)
