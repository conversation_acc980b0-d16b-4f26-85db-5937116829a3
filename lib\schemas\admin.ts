import { z } from "zod"
import {
  nameSchema,
  emailSchema,
  descriptionSchema,
  userRoleSchema,
  paginationSchema,
  enhancedPaginationSchema,
  dateTimeSchema,
  booleanFlagSchema,
  urlSchema,
  createFilteredQuerySchema,
  createCRUDSchemas
} from "./common"

// ============================================================================
// ADMIN-SPECIFIC SCHEMAS
// ============================================================================

// User management schemas
export const adminUserQuerySchema = enhancedPaginationSchema.extend({
  role: z.enum(['STUDENT', 'ADMIN', 'all']).optional().default('all'),
  sortBy: z.enum(['name', 'email', 'createdAt', 'lastActive']).optional().default('createdAt'),
  isActive: z.enum(['true', 'false', 'all']).optional().default('all'),
  lastActiveFrom: dateTimeSchema.optional(),
  lastActiveTo: dateTimeSchema.optional()
})

export const createUserSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  role: userRoleSchema.default('STUDENT'),
  bio: descriptionSchema,
  password: z.string().min(8, "Password must be at least 8 characters").optional()
})

export const updateUserSchema = createUserSchema.partial()

export const bulkUserOperationSchema = z.object({
  userIds: z.array(z.string()).min(1, "At least one user ID is required"),
  operation: z.enum(['activate', 'deactivate', 'delete', 'change_role']),
  newRole: userRoleSchema.optional(),
  confirmDeletion: booleanFlagSchema.optional()
}).refine(data => {
  if (data.operation === 'change_role') return data.newRole !== undefined
  if (data.operation === 'delete') return data.confirmDeletion === true
  return true
}, { message: "Invalid operation parameters" })

// File management schemas
export const adminFileQuerySchema = enhancedPaginationSchema.extend({
  type: z.enum(['image', 'document', 'general', 'all']).optional().default('all'),
  folder: z.string().optional(),
  uploadedBy: z.string().optional(),
  sizeMin: z.number().optional(),
  sizeMax: z.number().optional()
})

export const uploadFileSchema = z.object({
  filename: z.string().min(1, "Filename is required"),
  originalName: z.string().min(1, "Original name is required"),
  mimeType: z.string().min(1, "MIME type is required"),
  size: z.number().min(1, "File size must be greater than 0"),
  url: urlSchema,
  uploadType: z.enum(['image', 'document', 'general']).default('general'),
  folder: z.string().default('uploads')
})

export const updateFileSchema = z.object({
  originalName: z.string().min(1, "Original name is required").optional(),
  uploadType: z.enum(['image', 'document', 'general']).optional(),
  folder: z.string().optional()
})

// Notification management schemas
export const adminNotificationQuerySchema = enhancedPaginationSchema.extend({
  type: z.string().optional(),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).optional(),
  status: z.enum(['all', 'sent', 'scheduled', 'draft']).optional().default('all'),
  category: z.string().optional()
})

export const createNotificationSchema = z.object({
  type: z.enum(['info', 'success', 'warning', 'error', 'announcement']),
  title: z.string().min(1, "Title is required").max(200),
  message: z.string().min(1, "Message is required").max(1000),
  priority: z.enum(['low', 'normal', 'high', 'urgent']).default('normal'),
  category: z.string().optional(),
  actionUrl: urlSchema.optional(),
  imageUrl: urlSchema.optional(),
  expiresAt: dateTimeSchema.optional(),
  scheduledAt: dateTimeSchema.optional(),
  targetUsers: z.array(z.string()).optional(),
  targetRoles: z.array(userRoleSchema).optional(),
  sendToAll: booleanFlagSchema.default(false)
})

export const updateNotificationSchema = createNotificationSchema.partial()

// System settings schemas
export const systemSettingsSchema = z.object({
  siteName: z.string().min(1).max(100),
  siteDescription: z.string().max(500),
  siteUrl: urlSchema,
  adminEmail: emailSchema,
  allowRegistration: booleanFlagSchema,
  requireEmailVerification: booleanFlagSchema,
  defaultUserRole: userRoleSchema,
  maxFileSize: z.number().min(1).max(100),
  allowedFileTypes: z.array(z.string()),
  enableNotifications: booleanFlagSchema,
  enableAnalytics: booleanFlagSchema,
  maintenanceMode: booleanFlagSchema,
  theme: z.enum(['light', 'dark', 'system']),
  timezone: z.string(),
  language: z.string()
})

export const securitySettingsSchema = z.object({
  sessionTimeout: z.number().min(1).max(168), // 1 hour to 1 week
  maxLoginAttempts: z.number().min(1).max(10),
  passwordMinLength: z.number().min(6).max(50),
  requireStrongPassword: booleanFlagSchema,
  enableTwoFactor: booleanFlagSchema
})

export const emailSettingsSchema = z.object({
  smtpHost: z.string(),
  smtpPort: z.number().min(1).max(65535),
  smtpUser: z.string(),
  smtpPassword: z.string(),
  smtpSecure: booleanFlagSchema,
  fromEmail: emailSchema,
  fromName: z.string(),
  enableEmailNotifications: booleanFlagSchema
})

export const settingsUpdateSchema = z.object({
  system: systemSettingsSchema.partial().optional(),
  security: securitySettingsSchema.partial().optional(),
  email: emailSettingsSchema.partial().optional()
})

// Analytics schemas
export const adminAnalyticsQuerySchema = z.object({
  period: z.enum(['7d', '30d', '90d', '1y', 'all']).optional().default('30d'),
  startDate: dateTimeSchema.optional(),
  endDate: dateTimeSchema.optional(),
  groupBy: z.enum(['day', 'week', 'month']).optional().default('day'),
  metrics: z.array(z.string()).optional()
})

export const studentAnalyticsQuerySchema = adminAnalyticsQuerySchema.extend({
  userId: z.string().optional()
})

// PDF Export schemas
export const pdfExportQuerySchema = enhancedPaginationSchema.extend({
  type: z.string().optional(),
  status: z.enum(['all', 'pending', 'processing', 'completed', 'failed']).optional().default('all'),
  userId: z.string().optional()
})

export const bulkExportSchema = z.object({
  type: z.enum(['all-students', 'quiz-results', 'certificates']),
  filters: z.object({
    quizId: z.string().optional(),
    dateRange: z.object({
      start: dateTimeSchema,
      end: dateTimeSchema
    }).optional(),
    minScore: z.number().min(0).max(100).optional().default(60),
    includeIncomplete: booleanFlagSchema.default(false)
  }).optional(),
  format: z.enum(['pdf', 'csv', 'json']).optional().default('pdf'),
  template: z.string().optional().default('modern'),
  customOptions: z.object({
    headerText: z.string().optional(),
    footerText: z.string().optional(),
    logoUrl: urlSchema.optional(),
    backgroundColor: z.string().optional(),
    textColor: z.string().optional()
  }).optional()
})

// Scheduled quiz schemas
export const scheduledQuizQuerySchema = enhancedPaginationSchema.extend({
  status: z.enum(['all', 'active', 'upcoming', 'completed', 'expired']).optional().default('all')
})

// AI-related schemas
export const quizCreationRequestSchema = z.object({
  content: z.string().optional(),
  files: z.array(z.string()).optional(),
  requirements: z.object({
    questionCount: z.number().min(1).max(100),
    difficulty: z.enum(['EASY', 'MEDIUM', 'HARD']),
    questionTypes: z.array(z.enum(['MCQ', 'TRUE_FALSE', 'SHORT_ANSWER', 'MATCHING'])),
    timeLimit: z.number().min(1),
    subject: z.string(),
    chapter: z.string().optional(),
    topic: z.string().optional()
  }),
  preferences: z.object({
    includeExplanations: booleanFlagSchema.default(true),
    includeImages: booleanFlagSchema.default(false),
    language: z.enum(['ENGLISH', 'HINDI', 'BILINGUAL']).default('ENGLISH'),
    complexityLevel: z.enum(['BASIC', 'INTERMEDIATE', 'ADVANCED']).default('INTERMEDIATE')
  }).optional(),
  modelConfig: z.object({
    orchestratorModel: z.string().optional(),
    contentAnalyzer: z.string().optional(),
    questionGenerator: z.string().optional(),
    qualityEvaluator: z.string().optional()
  }).optional()
})

export const imageGenerationSchema = z.object({
  prompt: z.string().min(1, "Prompt is required").max(1000),
  size: z.enum(['256x256', '512x512', '1024x1024']).default('512x512'),
  quality: z.enum(['standard', 'hd']).default('standard'),
  style: z.enum(['vivid', 'natural']).default('natural'),
  n: z.number().min(1).max(4).default(1)
})

export const tagSuggestionSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  content: z.string().optional(),
  existingTags: z.array(z.string()).optional(),
  maxSuggestions: z.number().min(1).max(20).default(10)
})

// Test series generation schema
export const testSeriesSchema = z.object({
  testType: z.enum(['WEEKLY_TEST', 'MONTHLY_TEST', 'TEST_SERIES', 'DAILY_PRACTICE']),
  examPattern: z.string().optional(),
  syllabusCoverage: z.enum(['CURRENT_WEEK', 'CURRENT_MONTH', 'FULL_SYLLABUS', 'SPECIFIC_TOPICS']),
  subject: z.string(),
  chapter: z.string().optional(),
  topic: z.string().optional(),
  questionCount: z.number().min(5).max(200),
  difficulty: z.enum(['EASY', 'MEDIUM', 'HARD']),
  questionTypes: z.array(z.string()),
  timeLimit: z.number().min(5).max(300),
  institutionName: z.string().optional(),
  testDate: z.string().optional(),
  printFormat: booleanFlagSchema.default(false),
  includeAnswerKey: booleanFlagSchema.default(true),
  includeInstructions: booleanFlagSchema.default(true)
})

// Dashboard stats schema
export const dashboardStatsQuerySchema = z.object({
  period: z.enum(['today', '7d', '30d', '90d']).optional().default('30d'),
  includeCharts: booleanFlagSchema.default(true)
})

// Notification stats schema
export const notificationStatsQuerySchema = z.object({
  period: z.enum(['7d', '30d', '90d', 'all']).optional().default('30d')
})

// Export all admin schemas
export const adminSchemas = {
  // User management
  adminUserQuery: adminUserQuerySchema,
  createUser: createUserSchema,
  updateUser: updateUserSchema,
  bulkUserOperation: bulkUserOperationSchema,
  
  // File management
  adminFileQuery: adminFileQuerySchema,
  uploadFile: uploadFileSchema,
  updateFile: updateFileSchema,
  
  // Notifications
  adminNotificationQuery: adminNotificationQuerySchema,
  createNotification: createNotificationSchema,
  updateNotification: updateNotificationSchema,
  
  // Settings
  systemSettings: systemSettingsSchema,
  securitySettings: securitySettingsSchema,
  emailSettings: emailSettingsSchema,
  settingsUpdate: settingsUpdateSchema,
  
  // Analytics
  adminAnalyticsQuery: adminAnalyticsQuerySchema,
  studentAnalyticsQuery: studentAnalyticsQuerySchema,
  
  // PDF Exports
  pdfExportQuery: pdfExportQuerySchema,
  bulkExport: bulkExportSchema,
  
  // Scheduled quizzes
  scheduledQuizQuery: scheduledQuizQuerySchema,
  
  // AI features
  quizCreationRequest: quizCreationRequestSchema,
  imageGeneration: imageGenerationSchema,
  tagSuggestion: tagSuggestionSchema,
  testSeries: testSeriesSchema,
  
  // Dashboard
  dashboardStatsQuery: dashboardStatsQuerySchema,
  notificationStatsQuery: notificationStatsQuerySchema
}
