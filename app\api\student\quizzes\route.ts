import { NextRequest } from 'next/server'
import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { studentQuizQuerySchema } from '@/lib/schemas/student'
import { prisma } from '@/lib/prisma'

// GET /api/student/quizzes - Get available quizzes for students
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'STUDENT',
    validateQuery: studentQuizQuerySchema
  },
  async (request: NextRequest, { user, validatedQuery }) => {
    const {
      page,
      limit,
      search,
      type,
      difficulty,
      category,
      subjectId,
      chapterId,
      topicId,
      enrolled
    } = validatedQuery

    try {
      // Build where clause
      const where: any = {
        isPublished: true,
        // Only show quizzes that are currently available
        OR: [
          { startTime: null },
          { startTime: { lte: new Date() } }
        ],
        AND: [
          {
            OR: [
              { endTime: null },
              { endTime: { gte: new Date() } }
            ]
          }
        ]
      }

      // Add search filter
      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
          { tags: { has: search } }
        ]
      }

      // Add type filter
      if (type) {
        where.type = type
      }

      // Add difficulty filter
      if (difficulty) {
        where.difficulty = difficulty
      }

      // Add category filter (using tags)
      if (category) {
        where.tags = { has: category }
      }

      // Add subject/chapter/topic filters
      if (subjectId) {
        where.subjectId = subjectId
      }
      if (chapterId) {
        where.chapterId = chapterId
      }
      if (topicId) {
        where.topicId = topicId
      }

      // Handle enrollment filter
      if (enrolled === 'true') {
        where.enrollments = {
          some: {
            userId: user.id
          }
        }
      } else if (enrolled === 'false') {
        where.enrollments = {
          none: {
            userId: user.id
          }
        }
      }

      // Get total count
      const total = await prisma.quiz.count({ where })

      // Get quizzes with pagination
      const quizzes = await prisma.quiz.findMany({
        where,
        include: {
          creator: {
            select: {
              name: true,
              email: true
            }
          },
          _count: {
            select: {
              questions: true,
              attempts: true,
              enrollments: true
            }
          },
          enrollments: {
            where: { userId: user.id },
            select: { id: true }
          },
          attempts: {
            where: { userId: user.id },
            select: {
              id: true,
              score: true,
              percentage: true,
              completedAt: true
            },
            orderBy: { completedAt: 'desc' },
            take: 1
          }
        },
        orderBy: { createdAt: 'desc' },
        skip: (page - 1) * limit,
        take: limit
      })

      // Transform data for frontend
      const transformedQuizzes = quizzes.map(quiz => ({
        id: quiz.id,
        title: quiz.title,
        description: quiz.description,
        type: quiz.type,
        difficulty: quiz.difficulty,
        tags: quiz.tags,
        thumbnail: quiz.thumbnail,
        timeLimit: quiz.timeLimit,
        questionCount: quiz._count.questions,
        totalAttempts: quiz._count.attempts,
        enrollmentCount: quiz._count.enrollments,
        isEnrolled: quiz.enrollments.length > 0,
        lastAttempt: quiz.attempts.length > 0 ? {
          id: quiz.attempts[0].id,
          score: quiz.attempts[0].score,
          percentage: quiz.attempts[0].percentage,
          completedAt: quiz.attempts[0].completedAt?.toISOString() || null
        } : null,
        instructor: {
          name: quiz.creator.name || 'Unknown',
          email: quiz.creator.email
        },
        createdAt: quiz.createdAt.toISOString(),
        startTime: quiz.startTime?.toISOString() || null,
        endTime: quiz.endTime?.toISOString() || null
      }))

      const totalPages = Math.ceil(total / limit)
      const hasNext = page < totalPages
      const hasPrev = page > 1

      return APIResponse.paginated(
        transformedQuizzes,
        { page, limit, total, totalPages, hasNext, hasPrev },
        'Quizzes retrieved successfully'
      )

    } catch (error) {
      console.error('Error fetching student quizzes:', error)
      return APIResponse.error('Failed to fetch quizzes', 500)
    }
  }
)
