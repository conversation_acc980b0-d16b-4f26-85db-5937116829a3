"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { ChevronDown } from "lucide-react"
import { useState } from "react"

export interface NavigationItemData {
  title: string
  href: string
  icon: LucideIcon
  description?: string
  badge?: string
  children?: Array<{
    title: string
    href: string
    badge?: string
  }>
}

interface NavigationItemProps {
  item: NavigationItemData
  mobile?: boolean
  className?: string
  activeClassName?: string
  inactiveClassName?: string
}

export function NavigationItem({
  item,
  mobile = false,
  className,
  activeClassName,
  inactiveClassName
}: NavigationItemProps) {
  const pathname = usePathname()
  const [isExpanded, setIsExpanded] = useState(false)
  
  const isActive = pathname === item.href || 
    (item.children && item.children.some(child => pathname === child.href))
  
  const hasChildren = item.children && item.children.length > 0

  const baseClasses = cn(
    "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
    "hover:bg-accent hover:text-accent-foreground",
    className
  )

  const activeClasses = cn(
    "bg-accent text-accent-foreground",
    activeClassName
  )

  const inactiveClasses = cn(
    "text-muted-foreground",
    inactiveClassName
  )

  const linkClasses = cn(
    baseClasses,
    isActive ? activeClasses : inactiveClasses
  )

  if (hasChildren) {
    return (
      <div className="space-y-1">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className={linkClasses}
        >
          <item.icon className="h-4 w-4" />
          <span className="flex-1 text-left">{item.title}</span>
          {item.badge && (
            <Badge variant="secondary" className="text-xs">
              {item.badge}
            </Badge>
          )}
          <ChevronDown 
            className={cn(
              "h-4 w-4 transition-transform",
              isExpanded && "rotate-180"
            )} 
          />
        </button>
        
        {isExpanded && (
          <div className="ml-6 space-y-1">
            {item.children.map((child) => {
              const isChildActive = pathname === child.href
              return (
                <Link
                  key={child.href}
                  href={child.href}
                  className={cn(
                    "flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-colors",
                    "hover:bg-accent hover:text-accent-foreground",
                    isChildActive 
                      ? "bg-accent text-accent-foreground" 
                      : "text-muted-foreground"
                  )}
                >
                  <span className="flex-1">{child.title}</span>
                  {child.badge && (
                    <Badge variant="secondary" className="text-xs">
                      {child.badge}
                    </Badge>
                  )}
                </Link>
              )
            })}
          </div>
        )}
      </div>
    )
  }

  return (
    <Link href={item.href} className={linkClasses}>
      <item.icon className="h-4 w-4" />
      <span className="flex-1">{item.title}</span>
      {item.badge && (
        <Badge variant="secondary" className="text-xs">
          {item.badge}
        </Badge>
      )}
    </Link>
  )
}

interface NavigationListProps {
  items: NavigationItemData[]
  mobile?: boolean
  className?: string
  itemClassName?: string
  activeItemClassName?: string
  inactiveItemClassName?: string
}

export function NavigationList({
  items,
  mobile = false,
  className,
  itemClassName,
  activeItemClassName,
  inactiveItemClassName
}: NavigationListProps) {
  return (
    <div className={cn("space-y-2", className)}>
      {items.map((item) => (
        <NavigationItem
          key={item.href}
          item={item}
          mobile={mobile}
          className={itemClassName}
          activeClassName={activeItemClassName}
          inactiveClassName={inactiveItemClassName}
        />
      ))}
    </div>
  )
}
