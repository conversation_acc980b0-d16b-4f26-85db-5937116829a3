"use client"

import { ReactNode, useEffect } from "react"
import { useSession } from "next-auth/react"
import { LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { BaseSidebar } from "./base-sidebar"
import { BaseHeader } from "./base-header"
import { NavigationItemData } from "./navigation-item"
import { NotificationCenter } from "@/components/realtime/notification-center"
import { initializeSocket } from "@/lib/socket-client"

interface BaseLayoutProps {
  children: ReactNode
  
  // Branding
  logoHref: string
  logoIcon: LucideIcon
  logoTitle: string
  logoSubtitle: string
  
  // Navigation
  navigationItems: NavigationItemData[]
  
  // User data
  userData?: any
  userLoading?: boolean
  showUserStats?: boolean
  userProfileHref?: string
  userSettingsHref?: string
  
  // Role and permissions
  roleBadge?: string
  badgeVariant?: "default" | "secondary" | "destructive" | "outline"
  
  // Layout configuration
  sidebarWidth?: string
  showSidebar?: boolean
  showHeader?: boolean
  showNotifications?: boolean
  
  // Header customization
  headerLeftContent?: ReactNode
  headerCenterContent?: ReactNode
  headerRightContent?: ReactNode
  
  // Sidebar customization
  sidebarHeaderContent?: ReactNode
  sidebarFooterContent?: ReactNode
  mobileSidebarHeaderContent?: ReactNode
  mobileSidebarFooterContent?: ReactNode
  
  // Styling
  className?: string
  sidebarClassName?: string
  headerClassName?: string
  mainClassName?: string
  
  // Socket initialization
  enableSocket?: boolean
  socketUserData?: {
    id: string
    name: string
    email: string
    role: string
    token?: string
  }
}

export function BaseLayout({
  children,
  logoHref,
  logoIcon,
  logoTitle,
  logoSubtitle,
  navigationItems,
  userData,
  userLoading = false,
  showUserStats = false,
  userProfileHref,
  userSettingsHref,
  roleBadge,
  badgeVariant = "secondary",
  sidebarWidth = "w-64",
  showSidebar = true,
  showHeader = true,
  showNotifications = true,
  headerLeftContent,
  headerCenterContent,
  headerRightContent,
  sidebarHeaderContent,
  sidebarFooterContent,
  mobileSidebarHeaderContent,
  mobileSidebarFooterContent,
  className,
  sidebarClassName,
  headerClassName,
  mainClassName,
  enableSocket = true,
  socketUserData
}: BaseLayoutProps) {
  const { data: session } = useSession()

  // Initialize socket connection
  useEffect(() => {
    if (enableSocket && (socketUserData || session?.user)) {
      const userData = socketUserData || {
        id: session!.user.id,
        name: session!.user.name || 'User',
        email: session!.user.email || '',
        role: session!.user.role || 'STUDENT',
        token: 'session-token'
      }
      
      console.log('🔌 Initializing socket for user:', userData.name)
      initializeSocket(userData)
    }
  }, [enableSocket, socketUserData, session])

  const sidebarPaddingClass = showSidebar ? `md:pl-${sidebarWidth.replace('w-', '')}` : ''

  return (
    <div className={cn("min-h-screen bg-background", className)}>
      {/* Sidebar */}
      {showSidebar && (
        <BaseSidebar
          logoHref={logoHref}
          logoIcon={logoIcon}
          logoTitle={logoTitle}
          logoSubtitle={logoSubtitle}
          navigationItems={navigationItems}
          userData={userData}
          userLoading={userLoading}
          showUserStats={showUserStats}
          userProfileHref={userProfileHref}
          userSettingsHref={userSettingsHref}
          roleBadge={roleBadge}
          badgeVariant={badgeVariant}
          width={sidebarWidth}
          headerContent={sidebarHeaderContent}
          footerContent={sidebarFooterContent}
          className={sidebarClassName}
        />
      )}

      {/* Main Content Area */}
      <div className={cn(sidebarPaddingClass)}>
        {/* Header */}
        {showHeader && (
          <BaseHeader
            logoHref={logoHref}
            logoIcon={logoIcon}
            logoTitle={logoTitle}
            logoSubtitle={logoSubtitle}
            navigationItems={navigationItems}
            userData={userData}
            userLoading={userLoading}
            showUserStats={showUserStats}
            userProfileHref={userProfileHref}
            userSettingsHref={userSettingsHref}
            leftContent={headerLeftContent}
            centerContent={headerCenterContent}
            rightContent={
              <div className="flex items-center gap-4">
                {showNotifications && <NotificationCenter />}
                {headerRightContent}
              </div>
            }
            mobileSidebarHeaderContent={mobileSidebarHeaderContent}
            mobileSidebarFooterContent={mobileSidebarFooterContent}
            showMobileSidebar={showSidebar}
            className={headerClassName}
          />
        )}

        {/* Main Content */}
        <main className={cn("flex-1 overflow-auto", mainClassName)}>
          {children}
        </main>
      </div>
    </div>
  )
}

// Specialized layout variants for common use cases
interface AdminLayoutProps extends Omit<BaseLayoutProps, 'logoIcon' | 'logoTitle' | 'logoSubtitle' | 'roleBadge'> {
  customBranding?: {
    icon: LucideIcon
    title: string
    subtitle: string
  }
}

export function AdminBaseLayout({
  customBranding,
  ...props
}: AdminLayoutProps) {
  return (
    <BaseLayout
      logoIcon={customBranding?.icon || require('lucide-react').Shield}
      logoTitle={customBranding?.title || "Admin Panel"}
      logoSubtitle={customBranding?.subtitle || "QuizMaster"}
      roleBadge="ADMIN"
      badgeVariant="secondary"
      {...props}
    />
  )
}

interface StudentLayoutProps extends Omit<BaseLayoutProps, 'logoIcon' | 'logoTitle' | 'logoSubtitle' | 'roleBadge'> {
  customBranding?: {
    icon: LucideIcon
    title: string
    subtitle: string
  }
}

export function StudentBaseLayout({
  customBranding,
  ...props
}: StudentLayoutProps) {
  return (
    <BaseLayout
      logoIcon={customBranding?.icon || require('lucide-react').Target}
      logoTitle={customBranding?.title || "QuizMaster"}
      logoSubtitle={customBranding?.subtitle || "Student Portal"}
      showUserStats={true}
      {...props}
    />
  )
}

// Layout with search functionality
interface SearchLayoutProps extends BaseLayoutProps {
  searchPlaceholder?: string
  onSearch?: (query: string) => void
  searchValue?: string
}

export function SearchLayout({
  searchPlaceholder,
  onSearch,
  searchValue,
  headerCenterContent,
  ...props
}: SearchLayoutProps) {
  const searchContent = (
    <div className="hidden md:flex items-center gap-4 max-w-md w-full">
      <div className="relative flex-1">
        <input
          type="text"
          placeholder={searchPlaceholder || "Search..."}
          value={searchValue}
          onChange={(e) => onSearch?.(e.target.value)}
          className="w-full px-3 py-2 text-sm border rounded-md bg-background"
        />
      </div>
    </div>
  )

  return (
    <BaseLayout
      {...props}
      headerCenterContent={headerCenterContent || searchContent}
    />
  )
}

// Layout with breadcrumbs
interface BreadcrumbLayoutProps extends BaseLayoutProps {
  breadcrumbs: Array<{
    label: string
    href?: string
  }>
}

export function BreadcrumbLayout({
  breadcrumbs,
  headerLeftContent,
  ...props
}: BreadcrumbLayoutProps) {
  const breadcrumbContent = (
    <nav className="hidden md:flex items-center space-x-2 text-sm text-muted-foreground">
      {breadcrumbs.map((crumb, index) => (
        <div key={index} className="flex items-center space-x-2">
          {index > 0 && <span>/</span>}
          {crumb.href ? (
            <a href={crumb.href} className="hover:text-foreground">
              {crumb.label}
            </a>
          ) : (
            <span className="text-foreground">{crumb.label}</span>
          )}
        </div>
      ))}
    </nav>
  )

  return (
    <BaseLayout
      {...props}
      headerLeftContent={headerLeftContent || breadcrumbContent}
    />
  )
}
