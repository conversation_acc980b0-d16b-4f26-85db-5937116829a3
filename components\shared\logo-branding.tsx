"use client"

import Link from "next/link"
import { LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"

interface LogoBrandingProps {
  href: string
  icon: LucideIcon
  title: string
  subtitle: string
  className?: string
  iconClassName?: string
  titleClassName?: string
  subtitleClassName?: string
}

export function LogoBranding({
  href,
  icon: Icon,
  title,
  subtitle,
  className,
  iconClassName,
  titleClassName,
  subtitleClassName
}: LogoBrandingProps) {
  return (
    <Link href={href} className={cn("flex items-center gap-2", className)}>
      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
        <Icon className={cn("h-5 w-5 text-white", iconClassName)} />
      </div>
      <div>
        <h1 className={cn("font-bold text-lg", titleClassName)}>{title}</h1>
        <p className={cn("text-xs text-muted-foreground", subtitleClassName)}>{subtitle}</p>
      </div>
    </Link>
  )
}
