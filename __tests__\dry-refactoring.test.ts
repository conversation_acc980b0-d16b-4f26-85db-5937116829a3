// ============================================================================
// DRY REFACTORING VALIDATION TESTS
// ============================================================================
// These tests validate that our DRY refactoring is working correctly

import { describe, it, expect } from '@jest/globals'

// Test consolidated schemas
describe('Consolidated Schemas', () => {
  it('should export common schemas', async () => {
    const schemas = await import('../lib/schemas')
    
    // Test that common schemas are available
    expect(schemas.paginationSchema).toBeDefined()
    expect(schemas.emailSchema).toBeDefined()
    expect(schemas.nameSchema).toBeDefined()
    expect(schemas.quizQuerySchema).toBeDefined()
    expect(schemas.createQuizSchema).toBeDefined()
    expect(schemas.userQuerySchema).toBeDefined()
  })

  it('should validate pagination schema correctly', async () => {
    const { paginationSchema } = await import('../lib/schemas')
    
    const validData = { page: '1', limit: '20' }
    const result = paginationSchema.parse(validData)
    
    expect(result.page).toBe(1)
    expect(result.limit).toBe(20)
  })

  it('should validate quiz query schema correctly', async () => {
    const { quizQuerySchema } = await import('../lib/schemas/quiz')
    
    const validData = {
      page: '1',
      limit: '10',
      search: 'test',
      type: 'QUIZ',
      difficulty: 'EASY'
    }
    
    const result = quizQuerySchema.parse(validData)
    expect(result.search).toBe('test')
    expect(result.type).toBe('QUIZ')
    expect(result.difficulty).toBe('EASY')
  })
})

// Test shared components
describe('Shared Components', () => {
  it('should export layout components', async () => {
    const components = await import('../components/shared')
    
    expect(components.BaseLayout).toBeDefined()
    expect(components.AdminBaseLayout).toBeDefined()
    expect(components.StudentBaseLayout).toBeDefined()
    expect(components.LogoBranding).toBeDefined()
    expect(components.NavigationItem).toBeDefined()
    expect(components.UserProfileSection).toBeDefined()
  })

  it('should export UI components', async () => {
    const components = await import('../components/shared/ui')
    
    expect(components.FormLayout).toBeDefined()
    expect(components.LoadingSpinner).toBeDefined()
    expect(components.ErrorMessage).toBeDefined()
    expect(components.BaseCard).toBeDefined()
    expect(components.StatsCard).toBeDefined()
  })
})

// Test API utilities
describe('API Utilities', () => {
  it('should export API utilities', async () => {
    const utils = await import('../lib/api-utils')
    
    expect(utils.parseQueryParams).toBeDefined()
    expect(utils.calculatePagination).toBeDefined()
    expect(utils.checkRateLimit).toBeDefined()
    expect(utils.validateFileUpload).toBeDefined()
  })

  it('should calculate pagination correctly', async () => {
    const { calculatePagination } = await import('../lib/api-utils')
    
    const result = calculatePagination(1, 10, 25)
    
    expect(result.page).toBe(1)
    expect(result.limit).toBe(10)
    expect(result.total).toBe(25)
    expect(result.totalPages).toBe(3)
    expect(result.hasNext).toBe(true)
    expect(result.hasPrev).toBe(false)
    expect(result.offset).toBe(0)
  })

  it('should validate file upload correctly', async () => {
    const { validateFileUpload } = await import('../lib/api-utils')
    
    // Mock file object
    const mockFile = {
      name: 'test.jpg',
      size: 1024 * 1024, // 1MB
      type: 'image/jpeg'
    } as File

    const result = validateFileUpload(mockFile, {
      maxSize: 5 * 1024 * 1024, // 5MB
      allowedTypes: ['image/jpeg', 'image/png'],
      allowedExtensions: ['jpg', 'png']
    })

    expect(result.valid).toBe(true)
    expect(result.errors).toHaveLength(0)
  })
})

// Test consolidated utilities
describe('Consolidated Utilities', () => {
  it('should export utility functions', async () => {
    const utils = await import('../lib/utils')
    
    expect(utils.dateUtils).toBeDefined()
    expect(utils.fileUtils).toBeDefined()
    expect(utils.numberUtils).toBeDefined()
    expect(utils.stringUtils).toBeDefined()
    expect(utils.arrayUtils).toBeDefined()
    expect(utils.quizUtils).toBeDefined()
  })

  it('should format file sizes correctly', async () => {
    const { fileUtils } = await import('../lib/utils')
    
    expect(fileUtils.formatFileSize(1024)).toBe('1 KB')
    expect(fileUtils.formatFileSize(1024 * 1024)).toBe('1 MB')
    expect(fileUtils.formatFileSize(0)).toBe('0 Bytes')
  })

  it('should calculate quiz scores correctly', async () => {
    const { quizUtils } = await import('../lib/utils')
    
    expect(quizUtils.calculateScore(8, 10)).toBe(80)
    expect(quizUtils.calculateScore(0, 10)).toBe(0)
    expect(quizUtils.calculateScore(10, 10)).toBe(100)
  })

  it('should format dates correctly', async () => {
    const { dateUtils } = await import('../lib/utils')
    
    const testDate = new Date('2024-01-15T10:30:00Z')
    
    expect(dateUtils.formatDate(testDate, 'short')).toBeTruthy()
    expect(dateUtils.formatDate(testDate, 'long')).toBeTruthy()
    expect(dateUtils.formatDuration(90)).toBe('1h 30m')
    expect(dateUtils.formatDuration(45)).toBe('45m')
  })

  it('should manipulate arrays correctly', async () => {
    const { arrayUtils } = await import('../lib/utils')
    
    const testArray = [1, 2, 3, 4, 5]
    
    expect(arrayUtils.unique([1, 1, 2, 2, 3])).toEqual([1, 2, 3])
    expect(arrayUtils.chunk(testArray, 2)).toEqual([[1, 2], [3, 4], [5]])
    
    const shuffled = arrayUtils.shuffle(testArray)
    expect(shuffled).toHaveLength(testArray.length)
    expect(shuffled.sort()).toEqual(testArray)
  })
})

// Test shared hooks
describe('Shared Hooks', () => {
  it('should export custom hooks', async () => {
    const hooks = await import('../hooks/shared')
    
    expect(hooks.useApi).toBeDefined()
    expect(hooks.usePaginatedApi).toBeDefined()
    expect(hooks.useFormSubmit).toBeDefined()
    expect(hooks.useLocalStorage).toBeDefined()
    expect(hooks.useDebounce).toBeDefined()
    expect(hooks.useUserRole).toBeDefined()
    expect(hooks.useTimer).toBeDefined()
    expect(hooks.useSearch).toBeDefined()
  })
})

// Test API middleware enhancements
describe('Enhanced API Middleware', () => {
  it('should export enhanced middleware', async () => {
    const middleware = await import('../lib/api-middleware')
    
    expect(middleware.createAPIHandler).toBeDefined()
    expect(middleware.APIResponse).toBeDefined()
    expect(middleware.APIError).toBeDefined()
  })

  it('should create proper API responses', async () => {
    const { APIResponse } = await import('../lib/api-middleware')
    
    // Test success response
    const successResponse = APIResponse.success({ test: 'data' }, 'Success message')
    expect(successResponse).toBeDefined()
    
    // Test error response
    const errorResponse = APIResponse.error('Test error', 400, 'TEST_ERROR')
    expect(errorResponse).toBeDefined()
    
    // Test paginated response
    const paginatedResponse = APIResponse.paginated(
      [{ id: 1 }, { id: 2 }],
      { page: 1, limit: 10, total: 2, totalPages: 1, hasNext: false, hasPrev: false }
    )
    expect(paginatedResponse).toBeDefined()
  })
})

// Integration test to verify DRY principles
describe('DRY Integration', () => {
  it('should have eliminated schema duplication', async () => {
    // Import schemas from different locations
    const commonSchemas = await import('../lib/schemas/common')
    const quizSchemas = await import('../lib/schemas/quiz')
    const userSchemas = await import('../lib/schemas/user')
    
    // Verify that common schemas are reused
    expect(commonSchemas.emailSchema).toBeDefined()
    expect(commonSchemas.nameSchema).toBeDefined()
    expect(commonSchemas.paginationSchema).toBeDefined()
    
    // Verify that specific schemas extend common ones
    expect(quizSchemas.quizQuerySchema).toBeDefined()
    expect(userSchemas.userQuerySchema).toBeDefined()
  })

  it('should have consolidated utility functions', async () => {
    const utils = await import('../lib/utils')
    
    // Verify that all utility categories are available
    expect(utils.utils.date).toBeDefined()
    expect(utils.utils.file).toBeDefined()
    expect(utils.utils.number).toBeDefined()
    expect(utils.utils.string).toBeDefined()
    expect(utils.utils.array).toBeDefined()
    expect(utils.utils.quiz).toBeDefined()
  })

  it('should have shared component library', async () => {
    const shared = await import('../components/shared')
    
    // Verify that both layout and UI components are available
    expect(shared.BaseLayout).toBeDefined()
    expect(shared.FormLayout).toBeDefined()
    expect(shared.LoadingSpinner).toBeDefined()
    expect(shared.ErrorMessage).toBeDefined()
  })
})

// Performance test to ensure DRY refactoring doesn't impact performance
describe('Performance Impact', () => {
  it('should load schemas quickly', async () => {
    const start = performance.now()
    await import('../lib/schemas')
    const end = performance.now()
    
    // Should load in less than 100ms
    expect(end - start).toBeLessThan(100)
  })

  it('should load shared components quickly', async () => {
    const start = performance.now()
    await import('../components/shared')
    const end = performance.now()
    
    // Should load in less than 200ms
    expect(end - start).toBeLessThan(200)
  })
})
