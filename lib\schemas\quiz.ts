import { z } from "zod"
import {
  nameSchema,
  descriptionSchema,
  quizTypeSchema,
  difficultySchema,
  questionTypeSchema,
  tagsArraySchema,
  timeLimitSchema,
  maxAttemptsSchema,
  scoreSchema,
  dateTimeSchema,
  booleanFlagSchema,
  urlSchema,
  createCRUDSchemas,
  createFilteredQuerySchema
} from "./common"

// ============================================================================
// QUIZ-RELATED SCHEMAS
// ============================================================================

// Base question schema
export const baseQuestionSchema = z.object({
  type: questionTypeSchema,
  text: z.string().min(1, "Question text is required"),
  points: z.number().min(1, "Points must be at least 1").default(1),
  difficulty: difficultySchema.optional(),
  tags: tagsArraySchema.optional(),
  image: urlSchema.optional(),
  explanation: z.string().optional(),
  order: z.number().min(0).optional()
})

// MCQ question schema
export const mcqQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("MCQ"),
  options: z.array(z.string()).length(4, "MCQ must have exactly 4 options"),
  correctAnswer: z.enum(["A", "B", "C", "D"]),
  explanation: z.string().optional()
})

// True/False question schema
export const trueFalseQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("TRUE_FALSE"),
  correctAnswer: z.boolean(),
  explanation: z.string().optional()
})

// Short answer question schema
export const shortAnswerQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("SHORT_ANSWER"),
  correctAnswer: z.string().min(1, "Correct answer is required"),
  acceptableAnswers: z.array(z.string()).optional(),
  caseSensitive: booleanFlagSchema,
  explanation: z.string().optional()
})

// Matching question schema
export const matchingQuestionSchema = baseQuestionSchema.extend({
  type: z.literal("MATCHING"),
  pairs: z.array(z.object({
    left: z.string().min(1, "Left item is required"),
    right: z.string().min(1, "Right item is required")
  })).min(2, "At least 2 pairs required").max(6, "Maximum 6 pairs allowed"),
  explanation: z.string().optional()
})

// Union of all question types
export const questionSchema = z.discriminatedUnion("type", [
  mcqQuestionSchema,
  trueFalseQuestionSchema,
  shortAnswerQuestionSchema,
  matchingQuestionSchema
])

// Question array schemas
export const questionsArraySchema = z.array(questionSchema).min(1, "At least 1 question required")
export const flexibleQuestionsSchema = z.array(questionSchema).min(1).max(50, "Maximum 50 questions allowed")

// Base quiz schema
export const baseQuizSchema = z.object({
  title: nameSchema,
  description: descriptionSchema,
  type: quizTypeSchema,
  difficulty: difficultySchema,
  tags: tagsArraySchema.optional(),
  timeLimit: timeLimitSchema,
  maxAttempts: maxAttemptsSchema,
  passingScore: scoreSchema,
  instructions: z.string().optional(),
  thumbnail: urlSchema.optional(),
  startTime: dateTimeSchema.optional(),
  endTime: dateTimeSchema.optional(),
  isPublished: booleanFlagSchema,
  // Category fields
  subjectId: z.string().optional(),
  chapterId: z.string().optional(),
  topicId: z.string().optional()
})

// Create quiz schema (includes questions)
export const createQuizSchema = baseQuizSchema.extend({
  questions: questionsArraySchema
})

// Update quiz schema (all fields optional except questions which can be updated separately)
export const updateQuizSchema = baseQuizSchema.partial().extend({
  questions: questionsArraySchema.optional()
})

// Quiz query schema with specific filters
export const quizQuerySchema = createFilteredQuerySchema({
  subjectId: z.string().optional(),
  chapterId: z.string().optional(),
  topicId: z.string().optional(),
  enrolled: z.enum(['true', 'false']).optional(),
  creatorId: z.string().optional(),
  isPublished: z.enum(['true', 'false']).optional()
})

// Quiz attempt schemas
export const startQuizAttemptSchema = z.object({
  quizId: z.string().min(1, "Quiz ID is required")
})

export const submitAnswerSchema = z.object({
  questionId: z.string().min(1, "Question ID is required"),
  answer: z.union([
    z.string(), // For text answers
    z.boolean(), // For true/false
    z.array(z.string()), // For matching pairs
    z.enum(["A", "B", "C", "D"]) // For MCQ
  ]),
  timeSpent: z.number().min(0).optional() // Time spent on question in seconds
})

export const submitQuizAttemptSchema = z.object({
  attemptId: z.string().min(1, "Attempt ID is required"),
  answers: z.array(submitAnswerSchema),
  totalTimeSpent: z.number().min(0).optional() // Total time spent in seconds
})

// Quiz scheduling schemas
export const scheduleQuizSchema = z.object({
  quizId: z.string().min(1, "Quiz ID is required"),
  title: nameSchema,
  description: descriptionSchema,
  startTime: dateTimeSchema,
  endTime: dateTimeSchema,
  duration: z.number().min(1).optional(),
  maxAttempts: maxAttemptsSchema,
  allowLateSubmission: booleanFlagSchema,
  showResults: booleanFlagSchema,
  shuffleQuestions: booleanFlagSchema
}).refine(data => new Date(data.startTime) < new Date(data.endTime), {
  message: "End time must be after start time"
})

export const updateScheduledQuizSchema = scheduleQuizSchema.partial()

// Quiz analytics schemas
export const quizAnalyticsQuerySchema = z.object({
  quizId: z.string().optional(),
  dateFrom: dateTimeSchema.optional(),
  dateTo: dateTimeSchema.optional(),
  groupBy: z.enum(['day', 'week', 'month']).optional().default('day')
})

// Quiz export schemas
export const quizExportSchema = z.object({
  quizIds: z.array(z.string()).min(1, "At least one quiz ID required"),
  format: z.enum(['pdf', 'docx', 'json']).default('pdf'),
  includeAnswers: booleanFlagSchema,
  includeExplanations: booleanFlagSchema,
  includeAnalytics: booleanFlagSchema
})

// Quiz duplication schema
export const duplicateQuizSchema = z.object({
  title: nameSchema.optional(),
  includeQuestions: booleanFlagSchema.default(true),
  resetScheduling: booleanFlagSchema.default(true),
  markAsDraft: booleanFlagSchema.default(true)
})

// Quiz bulk operations schema
export const bulkQuizOperationSchema = z.object({
  quizIds: z.array(z.string()).min(1, "At least one quiz ID required"),
  operation: z.enum(['publish', 'unpublish', 'archive', 'delete']),
  confirmDeletion: z.boolean().optional() // Required for delete operation
}).refine(data => {
  if (data.operation === 'delete') {
    return data.confirmDeletion === true
  }
  return true
}, {
  message: "Deletion must be confirmed"
})

// Quiz statistics response schema
export const quizStatsSchema = z.object({
  totalQuizzes: z.number(),
  publishedQuizzes: z.number(),
  draftQuizzes: z.number(),
  totalAttempts: z.number(),
  averageScore: z.number(),
  completionRate: z.number(),
  popularQuizzes: z.array(z.object({
    id: z.string(),
    title: z.string(),
    attempts: z.number(),
    averageScore: z.number()
  }))
})

// Export all quiz-related schemas
export const quizSchemas = {
  // Question schemas
  question: questionSchema,
  mcqQuestion: mcqQuestionSchema,
  trueFalseQuestion: trueFalseQuestionSchema,
  shortAnswerQuestion: shortAnswerQuestionSchema,
  matchingQuestion: matchingQuestionSchema,
  questionsArray: questionsArraySchema,
  flexibleQuestions: flexibleQuestionsSchema,
  
  // Quiz schemas
  baseQuiz: baseQuizSchema,
  createQuiz: createQuizSchema,
  updateQuiz: updateQuizSchema,
  quizQuery: quizQuerySchema,
  
  // Attempt schemas
  startAttempt: startQuizAttemptSchema,
  submitAnswer: submitAnswerSchema,
  submitAttempt: submitQuizAttemptSchema,
  
  // Scheduling schemas
  scheduleQuiz: scheduleQuizSchema,
  updateScheduledQuiz: updateScheduledQuizSchema,
  
  // Analytics and operations
  quizAnalyticsQuery: quizAnalyticsQuerySchema,
  quizExport: quizExportSchema,
  duplicateQuiz: duplicateQuizSchema,
  bulkOperation: bulkQuizOperationSchema,
  quizStats: quizStatsSchema
}

// Legacy compatibility - re-export from main schemas file
export { questionSchema as Question } from "../schemas"
