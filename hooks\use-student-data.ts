import { useState, useEffect, useCallback } from 'react'
import { toast } from 'sonner'
import { useDataFetching, usePaginatedData } from './use-admin-data'

// ============================================================================
// SHARED STUDENT DATA HOOKS
// ============================================================================

// Student dashboard hook
export function useStudentDashboard() {
  return useDataFetching({
    endpoint: '/api/student/dashboard',
    onError: (error) => {
      console.error('Failed to fetch dashboard data:', error)
    }
  })
}

// Student quizzes browsing hook
export function useStudentQuizzes() {
  const {
    items: quizzes,
    pagination,
    loading,
    error,
    refresh,
    search,
    filter,
    goToPage,
    setItems
  } = usePaginatedData({
    endpoint: '/api/student/quizzes',
    initialParams: { limit: 12 }
  })

  const enrollInQuiz = useCallback(async (quizId: string) => {
    try {
      const response = await fetch(`/api/student/quizzes/${quizId}/enroll`, {
        method: 'POST'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to enroll in quiz')
      }

      // Update the quiz in the list to reflect enrollment
      setItems(prev => prev.map(quiz => 
        quiz.id === quizId ? { ...quiz, isEnrolled: true } : quiz
      ))
      
      toast.success('Successfully enrolled in quiz')
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [setItems])

  const unenrollFromQuiz = useCallback(async (quizId: string) => {
    try {
      const response = await fetch(`/api/student/quizzes/${quizId}/enroll`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to unenroll from quiz')
      }

      setItems(prev => prev.map(quiz => 
        quiz.id === quizId ? { ...quiz, isEnrolled: false } : quiz
      ))
      
      toast.success('Successfully unenrolled from quiz')
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [setItems])

  return {
    quizzes,
    pagination,
    loading,
    error,
    refresh,
    search,
    filter,
    goToPage,
    enrollInQuiz,
    unenrollFromQuiz
  }
}

// Student quiz history hook
export function useStudentHistory() {
  const {
    items: attempts,
    pagination,
    loading,
    error,
    refresh,
    search,
    filter,
    goToPage
  } = usePaginatedData({
    endpoint: '/api/student/history'
  })

  return {
    attempts,
    pagination,
    loading,
    error,
    refresh,
    search,
    filter,
    goToPage
  }
}

// Student favorites hook
export function useStudentFavorites() {
  const {
    items: favorites,
    pagination,
    loading,
    error,
    refresh,
    search,
    filter,
    goToPage,
    setItems
  } = usePaginatedData({
    endpoint: '/api/student/favorites'
  })

  const addToFavorites = useCallback(async (quizId: string) => {
    try {
      const response = await fetch('/api/student/favorites', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quizId, action: 'add' })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to add to favorites')
      }

      const result = await response.json()
      setItems(prev => [result.data, ...prev])
      toast.success('Added to favorites')
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [setItems])

  const removeFromFavorites = useCallback(async (quizId: string) => {
    try {
      const response = await fetch('/api/student/favorites', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ quizId, action: 'remove' })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to remove from favorites')
      }

      setItems(prev => prev.filter(fav => fav.quizId !== quizId))
      toast.success('Removed from favorites')
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [setItems])

  return {
    favorites,
    pagination,
    loading,
    error,
    refresh,
    search,
    filter,
    goToPage,
    addToFavorites,
    removeFromFavorites
  }
}

// Student analytics hook
export function useStudentAnalytics(timeRange: string = '30d') {
  return useDataFetching({
    endpoint: '/api/student/analytics',
    initialParams: { timeRange },
    onError: (error) => {
      console.error('Failed to fetch analytics data:', error)
    }
  })
}

// Student profile hook
export function useStudentProfile() {
  const [saving, setSaving] = useState(false)
  
  const {
    data: profile,
    loading,
    error,
    refresh,
    setData
  } = useDataFetching({
    endpoint: '/api/student/profile'
  })

  const updateProfile = useCallback(async (profileData: any) => {
    try {
      setSaving(true)
      const response = await fetch('/api/student/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(profileData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update profile')
      }

      const result = await response.json()
      setData(result.data)
      toast.success('Profile updated successfully')
      return result.data
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    } finally {
      setSaving(false)
    }
  }, [setData])

  return {
    profile,
    loading,
    error,
    saving,
    refresh,
    updateProfile
  }
}

// Practice sessions hook
export function usePracticeSessions() {
  const {
    items: sessions,
    pagination,
    loading,
    error,
    refresh,
    filter,
    goToPage,
    setItems
  } = usePaginatedData({
    endpoint: '/api/student/practice'
  })

  const startPracticeSession = useCallback(async (sessionData: any) => {
    try {
      const response = await fetch('/api/student/practice', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(sessionData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to start practice session')
      }

      const result = await response.json()
      setItems(prev => [result.data, ...prev])
      toast.success('Practice session started')
      return result.data
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [setItems])

  return {
    sessions,
    pagination,
    loading,
    error,
    refresh,
    filter,
    goToPage,
    startPracticeSession
  }
}

// Quiz attempt hook
export function useQuizAttempt(quizId: string) {
  const [attempt, setAttempt] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [submitting, setSubmitting] = useState(false)

  const startAttempt = useCallback(async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/student/quizzes/${quizId}/attempt`, {
        method: 'POST'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to start quiz attempt')
      }

      const result = await response.json()
      setAttempt(result.data)
      return result.data
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    } finally {
      setLoading(false)
    }
  }, [quizId])

  const submitAnswer = useCallback(async (questionId: string, answer: any) => {
    if (!attempt) return

    try {
      const response = await fetch(`/api/student/quizzes/${quizId}/attempt/${attempt.id}/answer`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ questionId, answer })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to submit answer')
      }

      const result = await response.json()
      setAttempt(prev => ({
        ...prev,
        answers: [...(prev?.answers || []), result.data]
      }))
      
      return result.data
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [quizId, attempt])

  const submitAttempt = useCallback(async (answers: any[]) => {
    if (!attempt) return

    try {
      setSubmitting(true)
      const response = await fetch(`/api/student/quizzes/${quizId}/attempt/${attempt.id}/submit`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ answers })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to submit quiz')
      }

      const result = await response.json()
      setAttempt(result.data)
      toast.success('Quiz submitted successfully')
      return result.data
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    } finally {
      setSubmitting(false)
    }
  }, [quizId, attempt])

  return {
    attempt,
    loading,
    submitting,
    startAttempt,
    submitAnswer,
    submitAttempt
  }
}

// Achievements hook
export function useStudentAchievements() {
  const {
    items: achievements,
    pagination,
    loading,
    error,
    refresh,
    filter,
    goToPage
  } = usePaginatedData({
    endpoint: '/api/student/achievements'
  })

  return {
    achievements,
    pagination,
    loading,
    error,
    refresh,
    filter,
    goToPage
  }
}

// Leaderboard hook
export function useLeaderboard(period: string = 'all_time') {
  return useDataFetching({
    endpoint: '/api/student/leaderboard',
    initialParams: { period },
    onError: (error) => {
      console.error('Failed to fetch leaderboard data:', error)
    }
  })
}
