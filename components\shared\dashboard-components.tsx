"use client"

import { ReactNode } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  TrendingUp, 
  TrendingDown, 
  MoreHorizontal, 
  RefreshCw,
  Calendar,
  Clock,
  Users,
  Target,
  Award,
  Activity,
  ChevronRight,
  ExternalLink
} from "lucide-react"
import { cn } from "@/lib/utils"
import Link from "next/link"

// ============================================================================
// SHARED DASHBOARD COMPONENTS
// ============================================================================

// Dashboard grid container
interface DashboardGridProps {
  children: ReactNode
  className?: string
}

export function DashboardGrid({ children, className }: DashboardGridProps) {
  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6", className)}>
      {children}
    </div>
  )
}

// Metric card with trend indicators
interface MetricCardProps {
  title: string
  value: string | number
  icon: React.ComponentType<{ className?: string }>
  trend?: {
    value: number
    label: string
    direction: 'up' | 'down' | 'neutral'
  }
  description?: string
  color?: string
  onClick?: () => void
  loading?: boolean
}

export function MetricCard({
  title,
  value,
  icon: Icon,
  trend,
  description,
  color = "text-primary",
  onClick,
  loading
}: MetricCardProps) {
  const getTrendColor = (direction: string) => {
    switch (direction) {
      case 'up': return 'text-green-600'
      case 'down': return 'text-red-600'
      default: return 'text-muted-foreground'
    }
  }

  const TrendIcon = trend?.direction === 'up' ? TrendingUp : trend?.direction === 'down' ? TrendingDown : null

  const CardComponent = onClick ? 'button' : 'div'

  return (
    <Card className={cn("hover:shadow-md transition-shadow", onClick && "cursor-pointer hover:bg-accent/50")}>
      <CardComponent onClick={onClick} className="w-full text-left">
        <CardContent className="p-6">
          {loading ? (
            <div className="animate-pulse space-y-3">
              <div className="h-4 bg-muted rounded w-3/4"></div>
              <div className="h-8 bg-muted rounded w-1/2"></div>
              <div className="h-3 bg-muted rounded w-full"></div>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <p className="text-sm font-medium text-muted-foreground">{title}</p>
                <p className="text-3xl font-bold">{value}</p>
                {trend && (
                  <div className={cn("flex items-center text-sm", getTrendColor(trend.direction))}>
                    {TrendIcon && <TrendIcon className="h-4 w-4 mr-1" />}
                    <span>{trend.value > 0 ? '+' : ''}{trend.value}%</span>
                    <span className="text-muted-foreground ml-1">{trend.label}</span>
                  </div>
                )}
                {description && (
                  <p className="text-xs text-muted-foreground">{description}</p>
                )}
              </div>
              <div className={cn("p-3 rounded-full bg-primary/10")}>
                <Icon className={cn("h-6 w-6", color)} />
              </div>
            </div>
          )}
        </CardContent>
      </CardComponent>
    </Card>
  )
}

// Activity feed component
interface ActivityItem {
  id: string
  type: string
  title: string
  description?: string
  timestamp: string
  user?: {
    name: string
    avatar?: string
  }
  metadata?: Record<string, any>
}

interface ActivityFeedProps {
  title: string
  activities: ActivityItem[]
  loading?: boolean
  onViewAll?: () => void
  emptyMessage?: string
}

export function ActivityFeed({
  title,
  activities,
  loading,
  onViewAll,
  emptyMessage = "No recent activity"
}: ActivityFeedProps) {
  const getActivityIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'quiz_completed': return Award
      case 'user_registered': return Users
      case 'quiz_created': return Target
      default: return Activity
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            {title}
          </CardTitle>
          {onViewAll && (
            <Button variant="outline" size="sm" onClick={onViewAll}>
              View All
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="animate-pulse flex items-start gap-3">
                <div className="h-8 w-8 bg-muted rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : activities.length === 0 ? (
          <p className="text-center text-muted-foreground py-8">{emptyMessage}</p>
        ) : (
          <ScrollArea className="h-80">
            <div className="space-y-4">
              {activities.map((activity) => {
                const ActivityIcon = getActivityIcon(activity.type)
                return (
                  <div key={activity.id} className="flex items-start gap-3">
                    <div className="p-2 bg-primary/10 rounded-full">
                      <ActivityIcon className="h-4 w-4 text-primary" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium truncate">{activity.title}</h4>
                        <span className="text-xs text-muted-foreground">
                          {new Date(activity.timestamp).toLocaleDateString()}
                        </span>
                      </div>
                      {activity.description && (
                        <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                          {activity.description}
                        </p>
                      )}
                      {activity.user && (
                        <p className="text-xs text-muted-foreground mt-1">
                          by {activity.user.name}
                        </p>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  )
}

// Progress card with visual progress bar
interface ProgressCardProps {
  title: string
  description?: string
  progress: number
  total: number
  label?: string
  color?: string
  showPercentage?: boolean
  icon?: React.ComponentType<{ className?: string }>
}

export function ProgressCard({
  title,
  description,
  progress,
  total,
  label,
  color = "bg-primary",
  showPercentage = true,
  icon: Icon
}: ProgressCardProps) {
  const percentage = total > 0 ? Math.round((progress / total) * 100) : 0

  return (
    <Card>
      <CardContent className="p-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center gap-2">
                {Icon && <Icon className="h-5 w-5 text-primary" />}
                <h3 className="font-semibold">{title}</h3>
              </div>
              {description && (
                <p className="text-sm text-muted-foreground mt-1">{description}</p>
              )}
            </div>
            {showPercentage && (
              <Badge variant="outline">{percentage}%</Badge>
            )}
          </div>
          
          <div className="space-y-2">
            <Progress value={percentage} className="h-2" />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>{progress} {label || 'completed'}</span>
              <span>{total} total</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Quick stats overview
interface QuickStatsProps {
  stats: {
    label: string
    value: string | number
    icon: React.ComponentType<{ className?: string }>
    color?: string
  }[]
  loading?: boolean
}

export function QuickStats({ stats, loading }: QuickStatsProps) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        return (
          <div key={index} className="text-center space-y-2">
            {loading ? (
              <div className="animate-pulse space-y-2">
                <div className="h-8 w-8 bg-muted rounded-full mx-auto"></div>
                <div className="h-6 bg-muted rounded w-16 mx-auto"></div>
                <div className="h-4 bg-muted rounded w-20 mx-auto"></div>
              </div>
            ) : (
              <>
                <div className={cn("p-2 rounded-full w-fit mx-auto", stat.color || "bg-primary/10")}>
                  <Icon className={cn("h-5 w-5", stat.color ? "text-white" : "text-primary")} />
                </div>
                <div className="text-2xl font-bold">{stat.value}</div>
                <div className="text-sm text-muted-foreground">{stat.label}</div>
              </>
            )}
          </div>
        )
      })}
    </div>
  )
}

// Recent items list
interface RecentItem {
  id: string
  title: string
  subtitle?: string
  timestamp: string
  status?: string
  href?: string
  onClick?: () => void
}

interface RecentItemsProps {
  title: string
  items: RecentItem[]
  loading?: boolean
  onViewAll?: () => void
  emptyMessage?: string
}

export function RecentItems({
  title,
  items,
  loading,
  onViewAll,
  emptyMessage = "No recent items"
}: RecentItemsProps) {
  const getStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'completed': return 'bg-green-100 text-green-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'failed': return 'bg-red-100 text-red-800'
      case 'pending': return 'bg-blue-100 text-blue-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{title}</CardTitle>
          {onViewAll && (
            <Button variant="outline" size="sm" onClick={onViewAll}>
              View All
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="animate-pulse flex items-center justify-between">
                <div className="space-y-2 flex-1">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </div>
                <div className="h-6 w-16 bg-muted rounded"></div>
              </div>
            ))}
          </div>
        ) : items.length === 0 ? (
          <p className="text-center text-muted-foreground py-8">{emptyMessage}</p>
        ) : (
          <div className="space-y-3">
            {items.map((item) => {
              const ItemComponent = item.href ? Link : 'div'
              const itemProps = item.href ? { href: item.href } : {}
              
              return (
                <ItemComponent
                  key={item.id}
                  {...itemProps}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg border hover:bg-accent/50 transition-colors",
                    (item.href || item.onClick) && "cursor-pointer"
                  )}
                  onClick={item.onClick}
                >
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium truncate">{item.title}</h4>
                    {item.subtitle && (
                      <p className="text-sm text-muted-foreground truncate">{item.subtitle}</p>
                    )}
                    <p className="text-xs text-muted-foreground">
                      {new Date(item.timestamp).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    {item.status && (
                      <Badge className={getStatusColor(item.status)}>
                        {item.status.replace('_', ' ')}
                      </Badge>
                    )}
                    {item.href && <ExternalLink className="h-4 w-4 text-muted-foreground" />}
                  </div>
                </ItemComponent>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// Dashboard section with header
interface DashboardSectionProps {
  title: string
  description?: string
  children: ReactNode
  actions?: ReactNode
  className?: string
}

export function DashboardSection({
  title,
  description,
  children,
  actions,
  className
}: DashboardSectionProps) {
  return (
    <div className={cn("space-y-6", className)}>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{title}</h2>
          {description && (
            <p className="text-muted-foreground mt-1">{description}</p>
          )}
        </div>
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
      </div>
      {children}
    </div>
  )
}
