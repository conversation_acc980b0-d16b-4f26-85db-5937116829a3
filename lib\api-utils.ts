import { NextRequest } from 'next/server'
import { z } from 'zod'

// ============================================================================
// API UTILITY FUNCTIONS
// ============================================================================

// Rate limiting utilities
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(
  identifier: string,
  limit: number,
  windowMs: number
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now()
  const key = identifier
  const current = rateLimitStore.get(key)

  if (!current || now > current.resetTime) {
    // Reset or initialize
    rateLimitStore.set(key, { count: 1, resetTime: now + windowMs })
    return { allowed: true, remaining: limit - 1, resetTime: now + windowMs }
  }

  if (current.count >= limit) {
    return { allowed: false, remaining: 0, resetTime: current.resetTime }
  }

  current.count++
  rateLimitStore.set(key, current)
  return { allowed: true, remaining: limit - current.count, resetTime: current.resetTime }
}

// Pagination utilities
export function calculatePagination(page: number, limit: number, total: number) {
  const totalPages = Math.ceil(total / limit)
  const hasNext = page < totalPages
  const hasPrev = page > 1

  return {
    page,
    limit,
    total,
    totalPages,
    hasNext,
    hasPrev,
    offset: (page - 1) * limit
  }
}

export function extractPaginationFromQuery(searchParams: URLSearchParams) {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1'))
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '20')))
  
  return { page, limit }
}

// Query parameter utilities
export function parseQueryParams(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const params: Record<string, any> = {}

  for (const [key, value] of searchParams.entries()) {
    // Handle array parameters (e.g., tags[]=value1&tags[]=value2)
    if (key.endsWith('[]')) {
      const arrayKey = key.slice(0, -2)
      if (!params[arrayKey]) params[arrayKey] = []
      params[arrayKey].push(value)
    } else {
      params[key] = value
    }
  }

  return params
}

export function parseFilters(searchParams: URLSearchParams) {
  const filters: Record<string, any> = {}
  
  for (const [key, value] of searchParams.entries()) {
    if (key.startsWith('filter_')) {
      const filterKey = key.replace('filter_', '')
      filters[filterKey] = value
    }
  }

  return filters
}

// Request body utilities
export async function parseRequestBody<T>(
  request: NextRequest,
  schema?: z.ZodSchema<T>
): Promise<T> {
  try {
    const body = await request.json()
    
    if (schema) {
      return schema.parse(body)
    }
    
    return body
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error(`Validation error: ${error.errors.map(e => e.message).join(', ')}`)
    }
    throw new Error('Invalid JSON in request body')
  }
}

// File upload utilities
export function validateFileUpload(
  file: File,
  options: {
    maxSize?: number
    allowedTypes?: string[]
    allowedExtensions?: string[]
  } = {}
) {
  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    allowedTypes = [],
    allowedExtensions = []
  } = options

  const errors: string[] = []

  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size exceeds ${maxSize} bytes`)
  }

  // Check MIME type
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`)
  }

  // Check file extension
  if (allowedExtensions.length > 0) {
    const extension = file.name.split('.').pop()?.toLowerCase()
    if (!extension || !allowedExtensions.includes(extension)) {
      errors.push(`File extension .${extension} is not allowed`)
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

// Search utilities
export function buildSearchQuery(
  searchTerm: string,
  searchFields: string[],
  options: {
    caseSensitive?: boolean
    exactMatch?: boolean
    fuzzy?: boolean
  } = {}
) {
  const { caseSensitive = false, exactMatch = false, fuzzy = false } = options

  if (!searchTerm.trim()) return {}

  let term = searchTerm.trim()
  
  if (!caseSensitive) {
    term = term.toLowerCase()
  }

  if (exactMatch) {
    return {
      OR: searchFields.map(field => ({
        [field]: { equals: term, mode: caseSensitive ? 'default' : 'insensitive' }
      }))
    }
  }

  if (fuzzy) {
    // Simple fuzzy search - you might want to use a more sophisticated algorithm
    const fuzzyTerm = term.split('').join('%')
    return {
      OR: searchFields.map(field => ({
        [field]: { contains: fuzzyTerm, mode: caseSensitive ? 'default' : 'insensitive' }
      }))
    }
  }

  // Default: contains search
  return {
    OR: searchFields.map(field => ({
      [field]: { contains: term, mode: caseSensitive ? 'default' : 'insensitive' }
    }))
  }
}

// Sorting utilities
export function parseSortParams(searchParams: URLSearchParams) {
  const sortBy = searchParams.get('sortBy') || searchParams.get('sort')
  const sortOrder = searchParams.get('sortOrder') || searchParams.get('order') || 'desc'

  if (!sortBy) return {}

  return {
    [sortBy]: sortOrder.toLowerCase() === 'asc' ? 'asc' : 'desc'
  }
}

// Cache utilities
const cache = new Map<string, { data: any; expiry: number }>()

export function getCachedData<T>(key: string): T | null {
  const cached = cache.get(key)
  
  if (!cached || Date.now() > cached.expiry) {
    cache.delete(key)
    return null
  }
  
  return cached.data
}

export function setCachedData<T>(key: string, data: T, ttlMs: number): void {
  cache.set(key, {
    data,
    expiry: Date.now() + ttlMs
  })
}

export function clearCache(pattern?: string): void {
  if (!pattern) {
    cache.clear()
    return
  }

  const regex = new RegExp(pattern)
  for (const key of cache.keys()) {
    if (regex.test(key)) {
      cache.delete(key)
    }
  }
}

// Bulk operation utilities
export async function processBulkOperation<T, R>(
  items: T[],
  operation: (item: T) => Promise<R>,
  options: {
    batchSize?: number
    continueOnError?: boolean
  } = {}
): Promise<Array<{ item: T; success: boolean; result?: R; error?: string }>> {
  const { batchSize = 10, continueOnError = true } = options
  const results: Array<{ item: T; success: boolean; result?: R; error?: string }> = []

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize)
    
    const batchPromises = batch.map(async (item) => {
      try {
        const result = await operation(item)
        return { item, success: true, result }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error'
        
        if (!continueOnError) {
          throw error
        }
        
        return { item, success: false, error: errorMessage }
      }
    })

    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)
  }

  return results
}

// Request context utilities
export function getClientIP(request: NextRequest): string {
  return (
    request.headers.get('x-forwarded-for')?.split(',')[0] ||
    request.headers.get('x-real-ip') ||
    request.headers.get('cf-connecting-ip') ||
    'unknown'
  )
}

export function getUserAgent(request: NextRequest): string {
  return request.headers.get('user-agent') || 'unknown'
}

export function getRequestId(request: NextRequest): string {
  return request.headers.get('x-request-id') || 
         request.headers.get('x-correlation-id') ||
         Math.random().toString(36).substring(2, 15)
}

// Response header utilities
export function addSecurityHeaders(response: Response): Response {
  response.headers.set('X-Content-Type-Options', 'nosniff')
  response.headers.set('X-Frame-Options', 'DENY')
  response.headers.set('X-XSS-Protection', '1; mode=block')
  response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
  
  return response
}

export function addCacheHeaders(
  response: Response,
  options: {
    maxAge?: number
    sMaxAge?: number
    staleWhileRevalidate?: number
    mustRevalidate?: boolean
  } = {}
): Response {
  const {
    maxAge = 0,
    sMaxAge,
    staleWhileRevalidate,
    mustRevalidate = false
  } = options

  const cacheDirectives = [`max-age=${maxAge}`]

  if (sMaxAge !== undefined) {
    cacheDirectives.push(`s-maxage=${sMaxAge}`)
  }

  if (staleWhileRevalidate !== undefined) {
    cacheDirectives.push(`stale-while-revalidate=${staleWhileRevalidate}`)
  }

  if (mustRevalidate) {
    cacheDirectives.push('must-revalidate')
  }

  response.headers.set('Cache-Control', cacheDirectives.join(', '))

  return response
}

// CORS utilities
export function setCORSHeaders(response: Response, origin?: string): Response {
  if (origin) {
    response.headers.set('Access-Control-Allow-Origin', origin)
  } else {
    response.headers.set('Access-Control-Allow-Origin', '*')
  }

  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, API-Version, X-Request-ID')
  response.headers.set('Access-Control-Max-Age', '86400')
  response.headers.set('Access-Control-Allow-Credentials', 'true')

  return response
}
