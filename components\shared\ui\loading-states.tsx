"use client"

import { ReactNode } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"
import { Loader2, RefreshCw } from "lucide-react"

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg"
  className?: string
}

export function LoadingSpinner({ size = "md", className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6",
    lg: "h-8 w-8"
  }

  return (
    <Loader2 className={cn("animate-spin", sizeClasses[size], className)} />
  )
}

interface LoadingOverlayProps {
  loading: boolean
  children: ReactNode
  className?: string
  spinnerSize?: "sm" | "md" | "lg"
  message?: string
}

export function LoadingOverlay({
  loading,
  children,
  className,
  spinnerSize = "md",
  message
}: LoadingOverlayProps) {
  return (
    <div className={cn("relative", className)}>
      {children}
      {loading && (
        <div className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="flex flex-col items-center gap-2">
            <LoadingSpinner size={spinnerSize} />
            {message && (
              <p className="text-sm text-muted-foreground">{message}</p>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

interface LoadingCardProps {
  title?: string
  description?: string
  className?: string
}

export function LoadingCard({ title, description, className }: LoadingCardProps) {
  return (
    <Card className={className}>
      <CardHeader>
        {title ? (
          <CardTitle>{title}</CardTitle>
        ) : (
          <Skeleton className="h-6 w-48" />
        )}
        {description ? (
          <CardDescription>{description}</CardDescription>
        ) : (
          <Skeleton className="h-4 w-64" />
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
      </CardContent>
    </Card>
  )
}

interface LoadingTableProps {
  rows?: number
  columns?: number
  className?: string
}

export function LoadingTable({ rows = 5, columns = 4, className }: LoadingTableProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {/* Table header */}
      <div className="flex space-x-4">
        {Array.from({ length: columns }).map((_, i) => (
          <Skeleton key={i} className="h-4 flex-1" />
        ))}
      </div>
      
      {/* Table rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton key={colIndex} className="h-4 flex-1" />
          ))}
        </div>
      ))}
    </div>
  )
}

interface LoadingListProps {
  items?: number
  showAvatar?: boolean
  className?: string
}

export function LoadingList({ items = 5, showAvatar = false, className }: LoadingListProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {Array.from({ length: items }).map((_, index) => (
        <div key={index} className="flex items-center space-x-4">
          {showAvatar && <Skeleton className="h-10 w-10 rounded-full" />}
          <div className="space-y-2 flex-1">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </div>
      ))}
    </div>
  )
}

interface LoadingButtonProps {
  loading: boolean
  children: ReactNode
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  disabled?: boolean
  className?: string
  onClick?: () => void
}

export function LoadingButton({
  loading,
  children,
  variant = "default",
  size = "default",
  disabled = false,
  className,
  onClick
}: LoadingButtonProps) {
  return (
    <Button
      variant={variant}
      size={size}
      disabled={disabled || loading}
      onClick={onClick}
      className={className}
    >
      {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
      {children}
    </Button>
  )
}

interface InlineLoadingProps {
  loading: boolean
  children: ReactNode
  fallback?: ReactNode
  className?: string
}

export function InlineLoading({
  loading,
  children,
  fallback,
  className
}: InlineLoadingProps) {
  if (loading) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        <LoadingSpinner size="sm" />
        {fallback || <span className="text-sm text-muted-foreground">Loading...</span>}
      </div>
    )
  }

  return <>{children}</>
}

interface ProgressLoadingProps {
  progress: number
  message?: string
  className?: string
}

export function ProgressLoading({
  progress,
  message,
  className
}: ProgressLoadingProps) {
  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex justify-between text-sm">
        <span>{message || "Loading..."}</span>
        <span>{Math.round(progress)}%</span>
      </div>
      <div className="w-full bg-secondary rounded-full h-2">
        <div
          className="bg-primary h-2 rounded-full transition-all duration-300"
          style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
        />
      </div>
    </div>
  )
}

interface RefreshButtonProps {
  onRefresh: () => void
  loading?: boolean
  className?: string
  size?: "sm" | "md" | "lg"
}

export function RefreshButton({
  onRefresh,
  loading = false,
  className,
  size = "md"
}: RefreshButtonProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  }

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={onRefresh}
      disabled={loading}
      className={className}
    >
      <RefreshCw className={cn(
        sizeClasses[size],
        loading && "animate-spin"
      )} />
    </Button>
  )
}

interface LoadingPageProps {
  title?: string
  description?: string
  className?: string
}

export function LoadingPage({
  title = "Loading...",
  description,
  className
}: LoadingPageProps) {
  return (
    <div className={cn(
      "flex flex-col items-center justify-center min-h-[400px] space-y-4",
      className
    )}>
      <LoadingSpinner size="lg" />
      <div className="text-center space-y-2">
        <h2 className="text-lg font-semibold">{title}</h2>
        {description && (
          <p className="text-sm text-muted-foreground max-w-md">
            {description}
          </p>
        )}
      </div>
    </div>
  )
}

interface SkeletonCardProps {
  showHeader?: boolean
  showFooter?: boolean
  lines?: number
  className?: string
}

export function SkeletonCard({
  showHeader = true,
  showFooter = false,
  lines = 3,
  className
}: SkeletonCardProps) {
  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader>
          <Skeleton className="h-6 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
      )}
      <CardContent className="space-y-3">
        {Array.from({ length: lines }).map((_, index) => (
          <Skeleton
            key={index}
            className={cn(
              "h-4",
              index === lines - 1 ? "w-2/3" : "w-full"
            )}
          />
        ))}
      </CardContent>
      {showFooter && (
        <div className="p-6 pt-0">
          <Skeleton className="h-9 w-20" />
        </div>
      )}
    </Card>
  )
}
