"use client"

import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Shield,
  Users,
  FileText,
  BarChart3,
  Settings,
  Clock,
  Award,
  Activity,
  Plus,
  Bell,
  Calendar,
  TrendingUp,
  Target
} from "lucide-react"
import {
  AdminPageHeader,
  QuickActions,
  LoadingState
} from "@/components/admin/shared-page-components"
import {
  ActivityFeed,
  ProgressCard,
  MetricCard,
  SystemStatusCard
} from "@/components/shared/dashboard-components"
import { useDashboardStats } from "@/hooks/use-admin-data"
import { Badge } from "@/components/ui/badge"



export default function AdminDashboard() {
  const { data: session } = useSession()
  const { data: dashboardStats, loading, refresh } = useDashboardStats()

  // Create stats array from API data
  const stats = dashboardStats ? [
    {
      title: "Total Users",
      value: dashboardStats.totalUsers.toLocaleString(),
      icon: Users,
      trend: {
        value: dashboardStats.trends.usersGrowth,
        label: "from last month",
        direction: (dashboardStats.trends.usersGrowth >= 0 ? "up" : "down") as "up" | "down" | "neutral"
      }
    },
    {
      title: "Active Quizzes",
      value: dashboardStats.totalQuizzes.toLocaleString(),
      icon: FileText,
      trend: {
        value: dashboardStats.trends.quizzesGrowth,
        label: "from last month",
        direction: (dashboardStats.trends.quizzesGrowth >= 0 ? "up" : "down") as "up" | "down" | "neutral"
      }
    },
    {
      title: "Quiz Attempts",
      value: dashboardStats.totalAttempts.toLocaleString(),
      icon: Activity,
      trend: {
        value: dashboardStats.trends.attemptsGrowth,
        label: "from last month",
        direction: (dashboardStats.trends.attemptsGrowth >= 0 ? "up" : "down") as "up" | "down" | "neutral"
      }
    },
    {
      title: "Avg. Score",
      value: `${dashboardStats.averageScore}%`,
      icon: TrendingUp,
      trend: {
        value: dashboardStats.trends.scoreImprovement,
        label: "improvement",
        direction: (dashboardStats.trends.scoreImprovement >= 0 ? "up" : "down") as "up" | "down" | "neutral"
      }
    },
  ] : []

  const quickActions = [
    { title: "Create Quiz", description: "Generate a new quiz", icon: Plus, href: "/admin/quizzes/create", color: "bg-blue-500" },
    { title: "Manage Users", description: "View and edit users", icon: Users, href: "/admin/users", color: "bg-green-500" },
    { title: "View Analytics", description: "Check performance", icon: BarChart3, href: "/admin/analytics-dashboard", color: "bg-purple-500" },
    { title: "System Settings", description: "Configure platform", icon: Settings, href: "/admin/settings", color: "bg-orange-500" },
  ]

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <LoadingState message="Loading dashboard..." />
      </div>
    )
  }

  if (!dashboardStats) {
    return (
      <div className="p-6 space-y-6">
        <LoadingState message="Failed to load dashboard data" />
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <AdminPageHeader
        title="Dashboard Overview"
        description={`Welcome back, ${session?.user?.name || 'Admin'}! Here's what's happening today.`}
        badge={{ text: "Admin Access", variant: "secondary" }}
        onRefresh={refresh}
        loading={loading}
      />

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <MetricCard key={index} {...stat} />
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <div className="lg:col-span-2">
          <QuickActions
            actions={quickActions}
            title="Quick Actions"
            description="Common administrative tasks and shortcuts"
          />
        </div>

        {/* Recent Activity */}
        <ActivityFeed
          title="Recent Activity"
          activities={dashboardStats?.recentActivity || []}
          loading={loading}
          emptyMessage="No recent activity"
        />
      </div>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Overview */}
        <div className="space-y-4">
          <ProgressCard
            title="Quiz Completion Rate"
            description="Percentage of started quizzes that are completed"
            progress={dashboardStats?.performance?.quizCompletionRate || 0}
            total={100}
            label="completed"
            icon={Target}
          />
          <ProgressCard
            title="User Engagement"
            description="Active user participation rate"
            progress={dashboardStats?.performance?.userEngagementRate || 0}
            total={100}
            label="engaged"
            icon={Users}
          />
        </div>

        {/* System Status */}
        <SystemStatusCard
          title="System Status"
          description="Current system health and status"
          status={dashboardStats?.systemStatus || {
            serverStatus: 'offline',
            databaseStatus: 'error',
            aiServiceStatus: 'inactive',
            storageUsed: 0,
            lastBackup: new Date().toISOString()
          }}
          loading={loading}
        />
      </div>
    </div>
  )
}
