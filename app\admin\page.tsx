"use client"

import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  Shield,
  Users,
  FileText,
  BarChart3,
  Settings,
  Clock,
  Award,
  Activity,
  Plus,
  Bell,
  Calendar,
  TrendingUp
} from "lucide-react"
import { AdminPageHeader, StatCard, QuickActions, LoadingState } from "@/components/admin/shared-page-components"
import { useDashboardStats } from "@/hooks/use-admin-data"
import { Badge } from "@/components/ui/badge"



export default function AdminDashboard() {
  const { data: session } = useSession()
  const { data: dashboardStats, loading, refresh } = useDashboardStats()

  // Create stats array from API data
  const stats = dashboardStats ? [
    {
      title: "Total Users",
      value: dashboardStats.totalUsers.toLocaleString(),
      icon: Users,
      change: `${dashboardStats.trends.usersGrowth >= 0 ? '+' : ''}${dashboardStats.trends.usersGrowth}%`,
      trend: dashboardStats.trends.usersGrowth >= 0 ? "up" as const : "down" as const
    },
    {
      title: "Active Quizzes",
      value: dashboardStats.totalQuizzes.toLocaleString(),
      icon: FileText,
      change: `${dashboardStats.trends.quizzesGrowth >= 0 ? '+' : ''}${dashboardStats.trends.quizzesGrowth}%`,
      trend: dashboardStats.trends.quizzesGrowth >= 0 ? "up" as const : "down" as const
    },
    {
      title: "Quiz Attempts",
      value: dashboardStats.totalAttempts.toLocaleString(),
      icon: Activity,
      change: `${dashboardStats.trends.attemptsGrowth >= 0 ? '+' : ''}${dashboardStats.trends.attemptsGrowth}%`,
      trend: (dashboardStats.trends.attemptsGrowth >= 0 ? "up" : "down") as "up" | "down"
    },
    {
      title: "Avg. Score",
      value: `${dashboardStats.averageScore}%`,
      icon: TrendingUp,
      change: `${dashboardStats.trends.scoreImprovement >= 0 ? '+' : ''}${dashboardStats.trends.scoreImprovement}%`,
      trend: (dashboardStats.trends.scoreImprovement >= 0 ? "up" : "down") as "up" | "down"
    },
  ] : []

  const quickActions = [
    { title: "Create Quiz", description: "Generate a new quiz", icon: Plus, href: "/admin/quizzes/create", color: "bg-blue-500" },
    { title: "Manage Users", description: "View and edit users", icon: Users, href: "/admin/users", color: "bg-green-500" },
    { title: "View Analytics", description: "Check performance", icon: BarChart3, href: "/admin/analytics-dashboard", color: "bg-purple-500" },
    { title: "System Settings", description: "Configure platform", icon: Settings, href: "/admin/settings", color: "bg-orange-500" },
  ]

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <LoadingState message="Loading dashboard..." />
      </div>
    )
  }

  if (!dashboardStats) {
    return (
      <div className="p-6 space-y-6">
        <LoadingState message="Failed to load dashboard data" />
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <AdminPageHeader
        title="Dashboard Overview"
        description={`Welcome back, ${session?.user?.name || 'Admin'}! Here's what's happening today.`}
        badge={{ text: "Admin Access", variant: "secondary" }}
        onRefresh={refresh}
        loading={loading}
      />

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <div className="lg:col-span-2">
          <QuickActions
            actions={quickActions}
            title="Quick Actions"
            description="Common administrative tasks and shortcuts"
          />
        </div>

        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest system activities and updates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dashboardStats.recentActivity.length > 0 ? (
                dashboardStats.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3">
                    <div className="flex-shrink-0">
                      {activity.type === 'quiz_created' && (
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <FileText className="h-4 w-4 text-blue-600" />
                        </div>
                      )}
                      {activity.type === 'quiz_attempt' && (
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <Activity className="h-4 w-4 text-green-600" />
                        </div>
                      )}
                      {activity.type === 'user_registered' && (
                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                          <Users className="h-4 w-4 text-purple-600" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium">{activity.title}</p>
                      <p className="text-xs text-muted-foreground truncate">{activity.description}</p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(activity.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground">No recent activity</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Performance Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Performance Overview
            </CardTitle>
            <CardDescription>
              Key metrics and performance indicators
            </CardDescription>
          </CardHeader>
          <CardContent>
            {dashboardStats ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Quiz Completion Rate</span>
                  <span className="text-sm text-muted-foreground">{dashboardStats.performance.quizCompletionRate}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div className="bg-primary h-2 rounded-full" style={{ width: `${dashboardStats.performance.quizCompletionRate}%` }}></div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Average Score</span>
                  <span className="text-sm text-muted-foreground">{dashboardStats.averageScore}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: `${dashboardStats.averageScore}%` }}></div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">User Engagement</span>
                  <span className="text-sm text-muted-foreground">{dashboardStats.performance.userEngagementRate}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: `${dashboardStats.performance.userEngagementRate}%` }}></div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-2 bg-muted rounded"></div>
                </div>
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-2 bg-muted rounded"></div>
                </div>
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                  <div className="h-2 bg-muted rounded"></div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* System Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              System Status
            </CardTitle>
            <CardDescription>
              Current system health and status
            </CardDescription>
          </CardHeader>
          <CardContent>
            {dashboardStats ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Server Status</span>
                  <Badge className={
                    dashboardStats.systemStatus.serverStatus === 'online'
                      ? "bg-green-500 hover:bg-green-600"
                      : dashboardStats.systemStatus.serverStatus === 'maintenance'
                      ? "bg-yellow-500 hover:bg-yellow-600"
                      : "bg-red-500 hover:bg-red-600"
                  }>
                    {dashboardStats.systemStatus.serverStatus.charAt(0).toUpperCase() + dashboardStats.systemStatus.serverStatus.slice(1)}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Database</span>
                  <Badge className={
                    dashboardStats.systemStatus.databaseStatus === 'healthy'
                      ? "bg-green-500 hover:bg-green-600"
                      : dashboardStats.systemStatus.databaseStatus === 'warning'
                      ? "bg-yellow-500 hover:bg-yellow-600"
                      : "bg-red-500 hover:bg-red-600"
                  }>
                    {dashboardStats.systemStatus.databaseStatus.charAt(0).toUpperCase() + dashboardStats.systemStatus.databaseStatus.slice(1)}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">AI Service</span>
                  <Badge className={
                    dashboardStats.systemStatus.aiServiceStatus === 'active'
                      ? "bg-green-500 hover:bg-green-600"
                      : dashboardStats.systemStatus.aiServiceStatus === 'inactive'
                      ? "bg-yellow-500 hover:bg-yellow-600"
                      : "bg-red-500 hover:bg-red-600"
                  }>
                    {dashboardStats.systemStatus.aiServiceStatus.charAt(0).toUpperCase() + dashboardStats.systemStatus.aiServiceStatus.slice(1)}
                  </Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Storage</span>
                  <Badge variant="outline">{dashboardStats.systemStatus.storageUsed}% Used</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Last Backup</span>
                  <span className="text-sm text-muted-foreground">
                    {new Date(dashboardStats.systemStatus.lastBackup).toLocaleString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      hour: 'numeric',
                      minute: '2-digit',
                      hour12: true
                    })}
                  </span>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <div key={i} className="flex items-center justify-between animate-pulse">
                    <div className="h-4 bg-muted rounded w-1/3"></div>
                    <div className="h-6 bg-muted rounded w-16"></div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
