"use client"

import { <PERSON>actNode, useState } from 'react'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  X, 
  AlertTriangle, 
  CheckCircle, 
  Info, 
  AlertCircle,
  Trash2,
  Edit,
  Eye,
  Download,
  Share2
} from "lucide-react"
import { cn } from "@/lib/utils"

// ============================================================================
// SHARED MODAL COMPONENTS
// ============================================================================

// Standard modal with consistent styling
interface StandardModalProps {
  title: string
  description?: string
  children: ReactNode
  trigger?: ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  showCloseButton?: boolean
  className?: string
}

export function StandardModal({
  title,
  description,
  children,
  trigger,
  open,
  onOpenChange,
  size = 'md',
  showCloseButton = true,
  className
}: StandardModalProps) {
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-[95vw] h-[95vh]'
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent className={cn(sizeClasses[size], className)}>
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            {title}
            {showCloseButton && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onOpenChange?.(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </DialogTitle>
          {description && <DialogDescription>{description}</DialogDescription>}
        </DialogHeader>
        <div className={cn(
          "py-4",
          size === 'full' && "flex-1 overflow-hidden"
        )}>
          {size === 'full' ? (
            <ScrollArea className="h-full">
              {children}
            </ScrollArea>
          ) : (
            children
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Confirmation modal for destructive actions
interface ConfirmationModalProps {
  title: string
  description: string
  confirmLabel?: string
  cancelLabel?: string
  onConfirm: () => void | Promise<void>
  trigger?: ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
  variant?: 'destructive' | 'warning' | 'info'
  loading?: boolean
}

export function ConfirmationModal({
  title,
  description,
  confirmLabel = "Confirm",
  cancelLabel = "Cancel",
  onConfirm,
  trigger,
  open,
  onOpenChange,
  variant = 'destructive',
  loading
}: ConfirmationModalProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleConfirm = async () => {
    try {
      setIsLoading(true)
      await onConfirm()
      onOpenChange?.(false)
    } catch (error) {
      console.error('Confirmation action failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getIcon = () => {
    switch (variant) {
      case 'destructive':
        return <AlertTriangle className="h-6 w-6 text-destructive" />
      case 'warning':
        return <AlertCircle className="h-6 w-6 text-yellow-600" />
      case 'info':
        return <Info className="h-6 w-6 text-blue-600" />
      default:
        return <AlertTriangle className="h-6 w-6 text-destructive" />
    }
  }

  const getButtonVariant = () => {
    switch (variant) {
      case 'destructive':
        return 'destructive'
      case 'warning':
        return 'default'
      case 'info':
        return 'default'
      default:
        return 'destructive'
    }
  }

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      {trigger && <AlertDialogTrigger asChild>{trigger}</AlertDialogTrigger>}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle className="flex items-center gap-2">
            {getIcon()}
            {title}
          </AlertDialogTitle>
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading || loading}>
            {cancelLabel}
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleConfirm}
            disabled={isLoading || loading}
            className={cn(
              getButtonVariant() === 'destructive' && "bg-destructive text-destructive-foreground hover:bg-destructive/90"
            )}
          >
            {isLoading || loading ? "Processing..." : confirmLabel}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}

// Side panel for detailed views
interface SidePanelProps {
  title: string
  description?: string
  children: ReactNode
  trigger?: ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
  side?: 'left' | 'right' | 'top' | 'bottom'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  actions?: ReactNode
}

export function SidePanel({
  title,
  description,
  children,
  trigger,
  open,
  onOpenChange,
  side = 'right',
  size = 'md',
  actions
}: SidePanelProps) {
  const sizeClasses = {
    sm: 'w-80',
    md: 'w-96',
    lg: 'w-[500px]',
    xl: 'w-[600px]'
  }

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      {trigger && <SheetTrigger asChild>{trigger}</SheetTrigger>}
      <SheetContent side={side} className={cn(sizeClasses[size], "flex flex-col")}>
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
          {description && <SheetDescription>{description}</SheetDescription>}
        </SheetHeader>
        
        <div className="flex-1 overflow-hidden py-4">
          <ScrollArea className="h-full">
            {children}
          </ScrollArea>
        </div>
        
        {actions && (
          <>
            <Separator />
            <div className="flex justify-end gap-2 pt-4">
              {actions}
            </div>
          </>
        )}
      </SheetContent>
    </Sheet>
  )
}

// Quick action modal with predefined actions
interface QuickActionModalProps {
  title: string
  description?: string
  item: any
  actions: {
    label: string
    icon?: React.ComponentType<{ className?: string }>
    onClick: (item: any) => void | Promise<void>
    variant?: 'default' | 'destructive' | 'outline' | 'secondary'
    disabled?: boolean
  }[]
  trigger?: ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function QuickActionModal({
  title,
  description,
  item,
  actions,
  trigger,
  open,
  onOpenChange
}: QuickActionModalProps) {
  const [loadingAction, setLoadingAction] = useState<string | null>(null)

  const handleAction = async (action: any) => {
    try {
      setLoadingAction(action.label)
      await action.onClick(item)
      onOpenChange?.(false)
    } catch (error) {
      console.error('Action failed:', error)
    } finally {
      setLoadingAction(null)
    }
  }

  return (
    <StandardModal
      title={title}
      description={description}
      trigger={trigger}
      open={open}
      onOpenChange={onOpenChange}
      size="sm"
    >
      <div className="space-y-4">
        <div className="grid grid-cols-1 gap-2">
          {actions.map((action, index) => {
            const Icon = action.icon
            const isLoading = loadingAction === action.label
            
            return (
              <Button
                key={index}
                variant={action.variant || 'outline'}
                onClick={() => handleAction(action)}
                disabled={action.disabled || isLoading}
                className="justify-start h-auto p-4"
              >
                <div className="flex items-center gap-3">
                  {Icon && <Icon className="h-4 w-4" />}
                  <div className="text-left">
                    <div className="font-medium">
                      {isLoading ? "Processing..." : action.label}
                    </div>
                  </div>
                </div>
              </Button>
            )
          })}
        </div>
      </div>
    </StandardModal>
  )
}

// Form modal with built-in form handling
interface FormModalProps {
  title: string
  description?: string
  children: ReactNode
  trigger?: ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
  onSubmit: (e: React.FormEvent) => void | Promise<void>
  submitLabel?: string
  cancelLabel?: string
  loading?: boolean
  size?: 'sm' | 'md' | 'lg' | 'xl'
}

export function FormModal({
  title,
  description,
  children,
  trigger,
  open,
  onOpenChange,
  onSubmit,
  submitLabel = "Save",
  cancelLabel = "Cancel",
  loading,
  size = 'md'
}: FormModalProps) {
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      await onSubmit(e)
      onOpenChange?.(false)
    } catch (error) {
      console.error('Form submission failed:', error)
    }
  }

  return (
    <StandardModal
      title={title}
      description={description}
      trigger={trigger}
      open={open}
      onOpenChange={onOpenChange}
      size={size}
      showCloseButton={false}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-4">
          {children}
        </div>
        
        <Separator />
        
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange?.(false)}
            disabled={loading}
          >
            {cancelLabel}
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? "Saving..." : submitLabel}
          </Button>
        </DialogFooter>
      </form>
    </StandardModal>
  )
}

// Success modal for completed actions
interface SuccessModalProps {
  title: string
  description: string
  open: boolean
  onOpenChange: (open: boolean) => void
  actions?: {
    label: string
    onClick: () => void
    variant?: 'default' | 'outline'
  }[]
}

export function SuccessModal({
  title,
  description,
  open,
  onOpenChange,
  actions
}: SuccessModalProps) {
  return (
    <StandardModal
      title=""
      open={open}
      onOpenChange={onOpenChange}
      size="sm"
      showCloseButton={false}
    >
      <div className="text-center space-y-4">
        <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle className="h-6 w-6 text-green-600" />
        </div>
        
        <div>
          <h3 className="text-lg font-semibold">{title}</h3>
          <p className="text-muted-foreground mt-1">{description}</p>
        </div>
        
        <div className="flex justify-center gap-2">
          {actions ? (
            actions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant || 'default'}
                onClick={action.onClick}
              >
                {action.label}
              </Button>
            ))
          ) : (
            <Button onClick={() => onOpenChange(false)}>
              Close
            </Button>
          )}
        </div>
      </div>
    </StandardModal>
  )
}

// Common modal actions
export const ModalActions = {
  view: (onClick: () => void) => ({
    label: "View Details",
    icon: Eye,
    onClick,
    variant: 'outline' as const
  }),
  
  edit: (onClick: () => void) => ({
    label: "Edit",
    icon: Edit,
    onClick,
    variant: 'default' as const
  }),
  
  delete: (onClick: () => void) => ({
    label: "Delete",
    icon: Trash2,
    onClick,
    variant: 'destructive' as const
  }),
  
  download: (onClick: () => void) => ({
    label: "Download",
    icon: Download,
    onClick,
    variant: 'outline' as const
  }),
  
  share: (onClick: () => void) => ({
    label: "Share",
    icon: Share2,
    onClick,
    variant: 'outline' as const
  })
}
