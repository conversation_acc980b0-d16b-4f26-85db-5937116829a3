"use client"

import { ReactNode } from "react"
import { Scroll<PERSON>rea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { LogoBranding } from "./logo-branding"
import { NavigationList, NavigationItemData } from "./navigation-item"
import { UserProfileSection } from "./user-profile-section"
import { LucideIcon } from "lucide-react"

interface BaseSidebarProps {
  // Branding
  logoHref: string
  logoIcon: LucideIcon
  logoTitle: string
  logoSubtitle: string
  
  // Navigation
  navigationItems: NavigationItemData[]
  
  // User section
  userData?: any
  userLoading?: boolean
  showUserStats?: boolean
  userProfileHref?: string
  userSettingsHref?: string
  
  // Badge
  roleBadge?: string
  badgeVariant?: "default" | "secondary" | "destructive" | "outline"
  
  // Styling
  className?: string
  width?: string
  backgroundColor?: string
  
  // Additional content
  headerContent?: ReactNode
  footerContent?: ReactNode
  
  // Behavior
  showUserSection?: boolean
}

export function BaseSidebar({
  logoHref,
  logoIcon,
  logoTitle,
  logoSubtitle,
  navigationItems,
  userData,
  userLoading = false,
  showUserStats = false,
  userProfileHref,
  userSettingsHref,
  roleBadge,
  badgeVariant = "secondary",
  className,
  width = "w-64",
  backgroundColor = "bg-background",
  headerContent,
  footerContent,
  showUserSection = true
}: BaseSidebarProps) {
  return (
    <div className={cn(
      "hidden md:flex md:flex-col md:fixed md:inset-y-0 z-50 border-r",
      width,
      backgroundColor,
      className
    )}>
      <div className="flex flex-col flex-1 min-h-0">
        {/* Header with Logo and Badge */}
        <div className="flex h-16 items-center gap-2 border-b px-6">
          <div className="flex items-center gap-2 flex-1">
            <LogoBranding
              href={logoHref}
              icon={logoIcon}
              title={logoTitle}
              subtitle={logoSubtitle}
            />
          </div>
          {roleBadge && (
            <Badge variant={badgeVariant} className="ml-auto">
              {roleBadge}
            </Badge>
          )}
        </div>

        {/* Additional header content */}
        {headerContent && (
          <div className="border-b px-6 py-4">
            {headerContent}
          </div>
        )}

        {/* Navigation */}
        <ScrollArea className="flex-1 px-4 py-4">
          <NavigationList items={navigationItems} />
        </ScrollArea>

        {/* User Section */}
        {showUserSection && (
          <div className="border-t px-4 py-4">
            <UserProfileSection
              userData={userData}
              loading={userLoading}
              showStats={showUserStats}
              profileHref={userProfileHref}
              settingsHref={userSettingsHref}
            />
          </div>
        )}

        {/* Additional footer content */}
        {footerContent && (
          <div className="border-t px-6 py-4">
            {footerContent}
          </div>
        )}
      </div>
    </div>
  )
}

// Mobile sidebar variant
interface MobileSidebarContentProps extends Omit<BaseSidebarProps, 'className' | 'width'> {
  className?: string
}

export function MobileSidebarContent({
  logoHref,
  logoIcon,
  logoTitle,
  logoSubtitle,
  navigationItems,
  userData,
  userLoading = false,
  showUserStats = false,
  userProfileHref,
  userSettingsHref,
  headerContent,
  footerContent,
  showUserSection = true,
  className
}: MobileSidebarContentProps) {
  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Mobile Header */}
      <div className="flex items-center gap-2 px-2 py-4">
        <LogoBranding
          href={logoHref}
          icon={logoIcon}
          title={logoTitle}
          subtitle={logoSubtitle}
        />
      </div>

      {/* Additional header content */}
      {headerContent && (
        <div className="px-2 py-4 border-t">
          {headerContent}
        </div>
      )}

      {/* Navigation */}
      <ScrollArea className="flex-1 px-2 py-4">
        <NavigationList items={navigationItems} mobile />
      </ScrollArea>

      {/* User Section */}
      {showUserSection && (
        <div className="px-2 py-4 border-t">
          <UserProfileSection
            userData={userData}
            loading={userLoading}
            showStats={showUserStats}
            profileHref={userProfileHref}
            settingsHref={userSettingsHref}
            showDropdown={false}
          />
        </div>
      )}

      {/* Additional footer content */}
      {footerContent && (
        <div className="px-2 py-4 border-t">
          {footerContent}
        </div>
      )}
    </div>
  )
}
