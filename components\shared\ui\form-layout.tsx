"use client"

import { ReactNode } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { Loader2 } from "lucide-react"

interface FormLayoutProps {
  title?: string
  description?: string
  children: ReactNode
  className?: string
  headerContent?: ReactNode
  footerContent?: ReactNode
}

export function FormLayout({
  title,
  description,
  children,
  className,
  headerContent,
  footerContent
}: FormLayoutProps) {
  return (
    <Card className={cn("w-full max-w-2xl mx-auto", className)}>
      {(title || description || headerContent) && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
          {headerContent}
        </CardHeader>
      )}
      <CardContent className="space-y-6">
        {children}
      </CardContent>
      {footerContent && (
        <>
          <Separator />
          <div className="p-6 pt-0">
            {footerContent}
          </div>
        </>
      )}
    </Card>
  )
}

interface FormSectionProps {
  title?: string
  description?: string
  children: ReactNode
  className?: string
}

export function FormSection({
  title,
  description,
  children,
  className
}: FormSectionProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && <h3 className="text-lg font-medium">{title}</h3>}
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  )
}

interface FormActionsProps {
  children: ReactNode
  className?: string
  align?: "left" | "center" | "right"
  spacing?: "sm" | "md" | "lg"
}

export function FormActions({
  children,
  className,
  align = "right",
  spacing = "md"
}: FormActionsProps) {
  const alignClasses = {
    left: "justify-start",
    center: "justify-center",
    right: "justify-end"
  }

  const spacingClasses = {
    sm: "gap-2",
    md: "gap-4",
    lg: "gap-6"
  }

  return (
    <div className={cn(
      "flex items-center",
      alignClasses[align],
      spacingClasses[spacing],
      className
    )}>
      {children}
    </div>
  )
}

interface SubmitButtonProps {
  loading?: boolean
  disabled?: boolean
  children: ReactNode
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
  onClick?: () => void
  type?: "button" | "submit" | "reset"
}

export function SubmitButton({
  loading = false,
  disabled = false,
  children,
  variant = "default",
  size = "default",
  className,
  onClick,
  type = "submit"
}: SubmitButtonProps) {
  return (
    <Button
      type={type}
      variant={variant}
      size={size}
      disabled={disabled || loading}
      onClick={onClick}
      className={className}
    >
      {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
      {children}
    </Button>
  )
}

interface CancelButtonProps {
  children: ReactNode
  onClick?: () => void
  disabled?: boolean
  className?: string
}

export function CancelButton({
  children,
  onClick,
  disabled = false,
  className
}: CancelButtonProps) {
  return (
    <Button
      type="button"
      variant="outline"
      disabled={disabled}
      onClick={onClick}
      className={className}
    >
      {children}
    </Button>
  )
}

interface FormFieldProps {
  label?: string
  description?: string
  error?: string
  required?: boolean
  children: ReactNode
  className?: string
}

export function FormField({
  label,
  description,
  error,
  required = false,
  children,
  className
}: FormFieldProps) {
  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </label>
      )}
      {children}
      {description && !error && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  )
}

interface FormGridProps {
  children: ReactNode
  columns?: 1 | 2 | 3 | 4
  gap?: "sm" | "md" | "lg"
  className?: string
}

export function FormGrid({
  children,
  columns = 2,
  gap = "md",
  className
}: FormGridProps) {
  const columnClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
  }

  const gapClasses = {
    sm: "gap-4",
    md: "gap-6",
    lg: "gap-8"
  }

  return (
    <div className={cn(
      "grid",
      columnClasses[columns],
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  )
}

interface FormStepsProps {
  currentStep: number
  totalSteps: number
  steps: Array<{
    title: string
    description?: string
  }>
  className?: string
}

export function FormSteps({
  currentStep,
  totalSteps,
  steps,
  className
}: FormStepsProps) {
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">
          Step {currentStep} of {totalSteps}
        </h3>
        <span className="text-sm text-muted-foreground">
          {Math.round((currentStep / totalSteps) * 100)}% Complete
        </span>
      </div>
      
      <div className="flex items-center space-x-4">
        {steps.map((step, index) => {
          const stepNumber = index + 1
          const isActive = stepNumber === currentStep
          const isCompleted = stepNumber < currentStep
          
          return (
            <div key={index} className="flex items-center">
              <div className={cn(
                "flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium",
                isActive && "bg-primary text-primary-foreground",
                isCompleted && "bg-primary text-primary-foreground",
                !isActive && !isCompleted && "bg-muted text-muted-foreground"
              )}>
                {stepNumber}
              </div>
              {index < steps.length - 1 && (
                <div className={cn(
                  "w-12 h-0.5 mx-2",
                  isCompleted ? "bg-primary" : "bg-muted"
                )} />
              )}
            </div>
          )
        })}
      </div>
      
      <div>
        <h4 className="font-medium">{steps[currentStep - 1]?.title}</h4>
        {steps[currentStep - 1]?.description && (
          <p className="text-sm text-muted-foreground">
            {steps[currentStep - 1].description}
          </p>
        )}
      </div>
    </div>
  )
}
