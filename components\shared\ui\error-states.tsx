"use client"

import { ReactNode } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { cn } from "@/lib/utils"
import {
  AlertTriangle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Home,
  ArrowLeft,
  Bug,
  Wifi,
  Shield,
  Clock
} from "lucide-react"

interface ErrorMessageProps {
  title?: string
  message: string
  variant?: "default" | "destructive" | "warning"
  className?: string
  onDismiss?: () => void
}

export function ErrorMessage({
  title,
  message,
  variant = "destructive",
  className,
  onDismiss
}: ErrorMessageProps) {
  const icons = {
    default: AlertCircle,
    destructive: XCircle,
    warning: AlertTriangle
  }

  const Icon = icons[variant]

  return (
    <Alert variant={variant} className={className}>
      <Icon className="h-4 w-4" />
      {title && <AlertTitle>{title}</AlertTitle>}
      <AlertDescription className="flex items-center justify-between">
        <span>{message}</span>
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="ml-2 h-auto p-1"
          >
            <XCircle className="h-4 w-4" />
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}

interface ErrorCardProps {
  title?: string
  description?: string
  error?: Error | string
  onRetry?: () => void
  onGoHome?: () => void
  className?: string
  showDetails?: boolean
}

export function ErrorCard({
  title = "Something went wrong",
  description,
  error,
  onRetry,
  onGoHome,
  className,
  showDetails = false
}: ErrorCardProps) {
  const errorMessage = error instanceof Error ? error.message : error

  return (
    <Card className={cn("w-full max-w-md mx-auto", className)}>
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-destructive/10 flex items-center justify-center">
          <XCircle className="h-6 w-6 text-destructive" />
        </div>
        <CardTitle className="text-lg">{title}</CardTitle>
        {description && (
          <CardDescription>{description}</CardDescription>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {errorMessage && (
          <div className="p-3 bg-muted rounded-md">
            <p className="text-sm text-muted-foreground">
              {showDetails ? errorMessage : "An unexpected error occurred"}
            </p>
          </div>
        )}
        
        <div className="flex flex-col sm:flex-row gap-2">
          {onRetry && (
            <Button onClick={onRetry} className="flex-1">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          )}
          {onGoHome && (
            <Button variant="outline" onClick={onGoHome} className="flex-1">
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

interface ErrorPageProps {
  title?: string
  description?: string
  statusCode?: number
  onRetry?: () => void
  onGoBack?: () => void
  onGoHome?: () => void
  className?: string
  children?: ReactNode
}

export function ErrorPage({
  title,
  description,
  statusCode,
  onRetry,
  onGoBack,
  onGoHome,
  className,
  children
}: ErrorPageProps) {
  const getDefaultContent = () => {
    switch (statusCode) {
      case 404:
        return {
          title: "Page Not Found",
          description: "The page you're looking for doesn't exist or has been moved.",
          icon: AlertCircle
        }
      case 403:
        return {
          title: "Access Denied",
          description: "You don't have permission to access this resource.",
          icon: Shield
        }
      case 500:
        return {
          title: "Server Error",
          description: "Something went wrong on our end. Please try again later.",
          icon: Bug
        }
      case 503:
        return {
          title: "Service Unavailable",
          description: "The service is temporarily unavailable. Please try again later.",
          icon: Clock
        }
      default:
        return {
          title: "Something went wrong",
          description: "An unexpected error occurred. Please try again.",
          icon: XCircle
        }
    }
  }

  const defaultContent = getDefaultContent()
  const Icon = defaultContent.icon

  return (
    <div className={cn(
      "flex flex-col items-center justify-center min-h-[60vh] px-4 text-center",
      className
    )}>
      <div className="space-y-6 max-w-md">
        <div className="mx-auto h-16 w-16 rounded-full bg-destructive/10 flex items-center justify-center">
          <Icon className="h-8 w-8 text-destructive" />
        </div>
        
        {statusCode && (
          <div className="text-6xl font-bold text-muted-foreground">
            {statusCode}
          </div>
        )}
        
        <div className="space-y-2">
          <h1 className="text-2xl font-bold">
            {title || defaultContent.title}
          </h1>
          <p className="text-muted-foreground">
            {description || defaultContent.description}
          </p>
        </div>

        {children}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {onGoBack && (
            <Button variant="outline" onClick={onGoBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
          )}
          {onRetry && (
            <Button onClick={onRetry}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          )}
          {onGoHome && (
            <Button variant="outline" onClick={onGoHome}>
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

interface NetworkErrorProps {
  onRetry?: () => void
  className?: string
}

export function NetworkError({ onRetry, className }: NetworkErrorProps) {
  return (
    <div className={cn("text-center space-y-4", className)}>
      <div className="mx-auto h-12 w-12 rounded-full bg-destructive/10 flex items-center justify-center">
        <Wifi className="h-6 w-6 text-destructive" />
      </div>
      <div className="space-y-2">
        <h3 className="font-semibold">Connection Error</h3>
        <p className="text-sm text-muted-foreground">
          Please check your internet connection and try again.
        </p>
      </div>
      {onRetry && (
        <Button onClick={onRetry} size="sm">
          <RefreshCw className="mr-2 h-4 w-4" />
          Retry
        </Button>
      )}
    </div>
  )
}

interface ValidationErrorProps {
  errors: Array<{
    field: string
    message: string
  }>
  className?: string
}

export function ValidationError({ errors, className }: ValidationErrorProps) {
  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="h-4 w-4" />
      <AlertTitle>Validation Error</AlertTitle>
      <AlertDescription>
        <ul className="mt-2 space-y-1">
          {errors.map((error, index) => (
            <li key={index} className="text-sm">
              <strong>{error.field}:</strong> {error.message}
            </li>
          ))}
        </ul>
      </AlertDescription>
    </Alert>
  )
}

interface ErrorBoundaryFallbackProps {
  error: Error
  resetError: () => void
  className?: string
}

export function ErrorBoundaryFallback({
  error,
  resetError,
  className
}: ErrorBoundaryFallbackProps) {
  return (
    <div className={cn(
      "flex flex-col items-center justify-center min-h-[400px] p-6 text-center",
      className
    )}>
      <div className="space-y-4 max-w-md">
        <div className="mx-auto h-16 w-16 rounded-full bg-destructive/10 flex items-center justify-center">
          <Bug className="h-8 w-8 text-destructive" />
        </div>
        
        <div className="space-y-2">
          <h2 className="text-xl font-semibold">Application Error</h2>
          <p className="text-muted-foreground">
            Something went wrong in the application. This error has been logged.
          </p>
        </div>

        <details className="text-left">
          <summary className="cursor-pointer text-sm text-muted-foreground hover:text-foreground">
            Show error details
          </summary>
          <pre className="mt-2 p-3 bg-muted rounded text-xs overflow-auto">
            {error.message}
            {error.stack && (
              <>
                {'\n\n'}
                {error.stack}
              </>
            )}
          </pre>
        </details>

        <Button onClick={resetError}>
          <RefreshCw className="mr-2 h-4 w-4" />
          Try Again
        </Button>
      </div>
    </div>
  )
}

interface InlineErrorProps {
  message: string
  className?: string
  size?: "sm" | "md"
}

export function InlineError({
  message,
  className,
  size = "sm"
}: InlineErrorProps) {
  const sizeClasses = {
    sm: "text-xs",
    md: "text-sm"
  }

  return (
    <div className={cn(
      "flex items-center gap-1 text-destructive",
      sizeClasses[size],
      className
    )}>
      <AlertCircle className="h-3 w-3 flex-shrink-0" />
      <span>{message}</span>
    </div>
  )
}
