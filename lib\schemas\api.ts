import { z } from "zod"
import {
  successResponseSchema,
  errorResponseSchema,
  createPaginatedResponseSchema,
  dateTimeSchema,
  booleanFlagSchema
} from "./common"

// ============================================================================
// API-SPECIFIC SCHEMAS
// ============================================================================

// API Response wrapper schemas
export const apiSuccessSchema = <T extends z.ZodTypeAny>(dataSchema: T) => z.object({
  success: z.literal(true),
  data: dataSchema,
  message: z.string().optional(),
  timestamp: z.string().datetime().default(() => new Date().toISOString())
})

export const apiErrorSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  code: z.string().optional(),
  details: z.any().optional(),
  timestamp: z.string().datetime().default(() => new Date().toISOString())
})

// Generic API response schema
export const apiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) => 
  z.union([apiSuccessSchema(dataSchema), apiErrorSchema])

// Paginated API response schema
export const paginatedApiResponseSchema = <T extends z.ZodTypeAny>(itemSchema: T) => z.object({
  success: z.literal(true),
  data: z.array(itemSchema),
  pagination: z.object({
    page: z.number().min(1),
    limit: z.number().min(1),
    total: z.number().min(0),
    totalPages: z.number().min(0),
    hasNext: z.boolean(),
    hasPrev: z.boolean()
  }),
  message: z.string().optional(),
  timestamp: z.string().datetime().default(() => new Date().toISOString())
})

// File upload schemas
export const fileUploadSchema = z.object({
  file: z.any(), // File object from FormData
  maxSize: z.number().optional().default(10 * 1024 * 1024), // 10MB default
  allowedTypes: z.array(z.string()).optional().default(['pdf', 'doc', 'docx', 'txt', 'jpg', 'png', 'jpeg']),
  folder: z.string().optional().default('uploads')
})

export const uploadedFileSchema = z.object({
  id: z.string(),
  filename: z.string(),
  originalName: z.string(),
  mimeType: z.string(),
  size: z.number(),
  url: z.string(),
  folder: z.string(),
  uploadedAt: dateTimeSchema,
  uploadedBy: z.string()
})

export const multipleFileUploadSchema = z.object({
  files: z.array(z.any()).min(1, "At least one file required"),
  maxSize: z.number().optional().default(10 * 1024 * 1024),
  allowedTypes: z.array(z.string()).optional(),
  folder: z.string().optional().default('uploads')
})

// Bulk operation schemas
export const bulkOperationRequestSchema = z.object({
  ids: z.array(z.string()).min(1, "At least one ID required"),
  operation: z.string().min(1, "Operation is required"),
  options: z.record(z.any()).optional()
})

export const bulkOperationResultSchema = z.object({
  total: z.number(),
  successful: z.number(),
  failed: z.number(),
  results: z.array(z.object({
    id: z.string(),
    success: z.boolean(),
    error: z.string().optional()
  }))
})

// Search and filter schemas
export const searchRequestSchema = z.object({
  query: z.string().min(1, "Search query is required"),
  filters: z.record(z.any()).optional(),
  sort: z.object({
    field: z.string(),
    order: z.enum(['asc', 'desc']).default('desc')
  }).optional(),
  pagination: z.object({
    page: z.number().min(1).default(1),
    limit: z.number().min(1).max(100).default(20)
  }).optional()
})

export const searchResultSchema = <T extends z.ZodTypeAny>(itemSchema: T) => z.object({
  items: z.array(itemSchema),
  total: z.number(),
  query: z.string(),
  filters: z.record(z.any()).optional(),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean()
  }),
  facets: z.record(z.array(z.object({
    value: z.string(),
    count: z.number()
  }))).optional()
})

// Export/Import schemas
export const exportRequestSchema = z.object({
  format: z.enum(['json', 'csv', 'xlsx', 'pdf']).default('json'),
  filters: z.record(z.any()).optional(),
  fields: z.array(z.string()).optional(), // Specific fields to export
  options: z.object({
    includeHeaders: booleanFlagSchema.default(true),
    dateFormat: z.string().optional().default('ISO'),
    timezone: z.string().optional().default('UTC')
  }).optional()
})

export const exportJobSchema = z.object({
  id: z.string(),
  status: z.enum(['pending', 'processing', 'completed', 'failed']),
  format: z.enum(['json', 'csv', 'xlsx', 'pdf']),
  progress: z.number().min(0).max(100).default(0),
  totalRecords: z.number().optional(),
  processedRecords: z.number().optional(),
  downloadUrl: z.string().optional(),
  error: z.string().optional(),
  createdAt: dateTimeSchema,
  completedAt: dateTimeSchema.optional(),
  expiresAt: dateTimeSchema.optional()
})

export const importRequestSchema = z.object({
  file: z.any(), // File object
  format: z.enum(['json', 'csv', 'xlsx']),
  options: z.object({
    hasHeaders: booleanFlagSchema.default(true),
    skipRows: z.number().min(0).default(0),
    mapping: z.record(z.string()).optional(), // Field mapping
    validateOnly: booleanFlagSchema.default(false) // Only validate, don't import
  }).optional()
})

export const importJobSchema = z.object({
  id: z.string(),
  status: z.enum(['pending', 'processing', 'completed', 'failed']),
  format: z.enum(['json', 'csv', 'xlsx']),
  progress: z.number().min(0).max(100).default(0),
  totalRecords: z.number().optional(),
  processedRecords: z.number().optional(),
  successfulRecords: z.number().optional(),
  failedRecords: z.number().optional(),
  errors: z.array(z.object({
    row: z.number(),
    field: z.string().optional(),
    message: z.string()
  })).optional(),
  createdAt: dateTimeSchema,
  completedAt: dateTimeSchema.optional()
})

// Analytics and reporting schemas
export const analyticsQuerySchema = z.object({
  metrics: z.array(z.string()).min(1, "At least one metric required"),
  dimensions: z.array(z.string()).optional(),
  filters: z.record(z.any()).optional(),
  dateRange: z.object({
    start: dateTimeSchema,
    end: dateTimeSchema
  }),
  granularity: z.enum(['hour', 'day', 'week', 'month']).default('day')
})

export const analyticsResultSchema = z.object({
  metrics: z.record(z.number()),
  dimensions: z.record(z.any()).optional(),
  timeSeries: z.array(z.object({
    timestamp: dateTimeSchema,
    values: z.record(z.number())
  })).optional(),
  summary: z.object({
    total: z.record(z.number()),
    average: z.record(z.number()),
    change: z.record(z.object({
      value: z.number(),
      percentage: z.number()
    }))
  }).optional()
})

// Webhook schemas
export const webhookEventSchema = z.object({
  id: z.string(),
  type: z.string(),
  data: z.record(z.any()),
  timestamp: dateTimeSchema,
  source: z.string().optional()
})

export const webhookConfigSchema = z.object({
  url: z.string().url("Invalid webhook URL"),
  events: z.array(z.string()).min(1, "At least one event type required"),
  secret: z.string().optional(),
  active: booleanFlagSchema.default(true),
  retryPolicy: z.object({
    maxRetries: z.number().min(0).max(10).default(3),
    backoffMultiplier: z.number().min(1).default(2),
    initialDelay: z.number().min(1000).default(1000) // milliseconds
  }).optional()
})

// Rate limiting schemas
export const rateLimitSchema = z.object({
  limit: z.number().min(1),
  window: z.number().min(1), // seconds
  remaining: z.number().min(0),
  resetTime: dateTimeSchema
})

// API versioning schemas
export const apiVersionSchema = z.object({
  version: z.string().regex(/^v\d+(\.\d+)?$/, "Invalid version format"),
  deprecated: booleanFlagSchema.default(false),
  deprecationDate: dateTimeSchema.optional(),
  supportedUntil: dateTimeSchema.optional()
})

// Health check schemas
export const healthCheckSchema = z.object({
  status: z.enum(['healthy', 'degraded', 'unhealthy']),
  timestamp: dateTimeSchema,
  services: z.record(z.object({
    status: z.enum(['up', 'down', 'degraded']),
    responseTime: z.number().optional(),
    error: z.string().optional()
  })),
  version: z.string(),
  uptime: z.number() // seconds
})

// Export all API schemas
export const apiSchemas = {
  // Response schemas
  success: apiSuccessSchema,
  error: apiErrorSchema,
  response: apiResponseSchema,
  paginatedResponse: paginatedApiResponseSchema,
  
  // File operations
  fileUpload: fileUploadSchema,
  uploadedFile: uploadedFileSchema,
  multipleFileUpload: multipleFileUploadSchema,
  
  // Bulk operations
  bulkOperationRequest: bulkOperationRequestSchema,
  bulkOperationResult: bulkOperationResultSchema,
  
  // Search and filtering
  searchRequest: searchRequestSchema,
  searchResult: searchResultSchema,
  
  // Export/Import
  exportRequest: exportRequestSchema,
  exportJob: exportJobSchema,
  importRequest: importRequestSchema,
  importJob: importJobSchema,
  
  // Analytics
  analyticsQuery: analyticsQuerySchema,
  analyticsResult: analyticsResultSchema,
  
  // Webhooks
  webhookEvent: webhookEventSchema,
  webhookConfig: webhookConfigSchema,
  
  // System
  rateLimit: rateLimitSchema,
  apiVersion: apiVersionSchema,
  healthCheck: healthCheckSchema
}
