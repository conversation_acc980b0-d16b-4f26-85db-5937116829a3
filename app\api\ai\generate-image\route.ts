import { NextRequest } from 'next/server'
import { createAP<PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { imageGenerationSchema } from '@/lib/schemas'
import { createOpenAI } from '@ai-sdk/openai'
import { experimental_generateImage as generateImage } from 'ai'

// POST /api/ai/generate-image - Generate images using AI
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: imageGenerationSchema
  },
  async (request: NextRequest, { validatedBody, user }) => {
    try {
      const { prompt, size, quality, style, n } = validatedBody

      // Create OpenAI client
      if (!process.env.OPENAI_API_KEY) {
        return APIResponse.error('OpenAI API key not configured', 503)
      }
      
      const openai = createOpenAI({
        apiKey: process.env.OPENAI_API_KEY
      })

      // Generate image(s)
      const result = await generateImage({
        model: openai.image('dall-e-3'),
        prompt: prompt,
        size: size as any,
        n: n,
        providerOptions: {
          openai: {
            quality: quality,
            style: style
          }
        }
      })

      // Handle single or multiple images
      if (n === 1) {
        return APIResponse.success({
          image: {
            base64: result.image.base64,
            prompt: prompt,
            size: size,
            quality: quality,
            style: style,
            generatedAt: new Date().toISOString()
          }
        }, 'Image generated successfully')
      } else {
        return APIResponse.success({
          images: result.images.map((image, index) => ({
            base64: image.base64,
            prompt: prompt,
            size: size,
            quality: quality,
            style: style,
            index: index,
            generatedAt: new Date().toISOString()
          }))
        }, `${n} images generated successfully`)
      }
    } catch (error) {
      console.error('Error generating image:', error)
      
      // Handle specific AI SDK errors
      if (error instanceof Error) {
        if (error.message.includes('content_policy_violation')) {
          return APIResponse.error('Image generation failed due to content policy violation. Please modify your prompt.', 400)
        }
        if (error.message.includes('rate_limit_exceeded')) {
          return APIResponse.error('Rate limit exceeded. Please try again later.', 429)
        }
        if (error.message.includes('insufficient_quota')) {
          return APIResponse.error('Insufficient OpenAI quota. Please check your billing.', 402)
        }
      }
      
      return APIResponse.error('Failed to generate image', 500)
    }
  }
)
