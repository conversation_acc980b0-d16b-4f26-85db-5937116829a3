"use client"

import { ReactNode, forwardRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AlertCircle, Check, X, Upload, Eye, EyeOff } from "lucide-react"
import { cn } from "@/lib/utils"
import { useState } from 'react'

// ============================================================================
// SHARED FORM COMPONENTS
// ============================================================================

// Form container with consistent styling
interface FormContainerProps {
  title: string
  description?: string
  children: ReactNode
  actions?: ReactNode
  loading?: boolean
  className?: string
}

export function FormContainer({
  title,
  description,
  children,
  actions,
  loading,
  className
}: FormContainerProps) {
  return (
    <Card className={cn("w-full max-w-2xl mx-auto", className)}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent className="space-y-6">
        <div className={cn("space-y-4", loading && "opacity-50 pointer-events-none")}>
          {children}
        </div>
        {actions && (
          <>
            <Separator />
            <div className="flex justify-end gap-2">
              {actions}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}

// Enhanced input field with validation states
interface FormInputProps {
  label: string
  description?: string
  error?: string
  required?: boolean
  success?: boolean
  icon?: React.ComponentType<{ className?: string }>
  children: ReactNode
}

export function FormInput({
  label,
  description,
  error,
  required,
  success,
  icon: Icon,
  children
}: FormInputProps) {
  return (
    <div className="space-y-2">
      <Label className="flex items-center gap-2">
        {Icon && <Icon className="h-4 w-4" />}
        {label}
        {required && <span className="text-destructive">*</span>}
        {success && <Check className="h-4 w-4 text-green-600" />}
      </Label>
      {children}
      {description && !error && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
      {error && (
        <p className="text-sm text-destructive flex items-center gap-1">
          <AlertCircle className="h-4 w-4" />
          {error}
        </p>
      )}
    </div>
  )
}

// Password input with visibility toggle
interface PasswordInputProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  disabled?: boolean
  className?: string
}

export function PasswordInput({
  value,
  onChange,
  placeholder = "Enter password",
  disabled,
  className
}: PasswordInputProps) {
  const [showPassword, setShowPassword] = useState(false)

  return (
    <div className="relative">
      <Input
        type={showPassword ? "text" : "password"}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        disabled={disabled}
        className={cn("pr-10", className)}
      />
      <Button
        type="button"
        variant="ghost"
        size="sm"
        className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
        onClick={() => setShowPassword(!showPassword)}
        disabled={disabled}
      >
        {showPassword ? (
          <EyeOff className="h-4 w-4" />
        ) : (
          <Eye className="h-4 w-4" />
        )}
      </Button>
    </div>
  )
}

// File upload component
interface FileUploadProps {
  onFileSelect: (files: FileList) => void
  accept?: string
  multiple?: boolean
  maxSize?: number // in MB
  disabled?: boolean
  children?: ReactNode
}

export function FileUpload({
  onFileSelect,
  accept,
  multiple,
  maxSize = 10,
  disabled,
  children
}: FileUploadProps) {
  const [dragOver, setDragOver] = useState(false)

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    if (disabled) return
    
    const files = e.dataTransfer.files
    if (files.length > 0) {
      // Check file sizes
      const validFiles = Array.from(files).filter(file => 
        file.size <= maxSize * 1024 * 1024
      )
      
      if (validFiles.length > 0) {
        const fileList = new DataTransfer()
        validFiles.forEach(file => fileList.items.add(file))
        onFileSelect(fileList.files)
      }
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      onFileSelect(e.target.files)
    }
  }

  return (
    <div
      className={cn(
        "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
        dragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25",
        disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-primary"
      )}
      onDrop={handleDrop}
      onDragOver={(e) => {
        e.preventDefault()
        if (!disabled) setDragOver(true)
      }}
      onDragLeave={() => setDragOver(false)}
    >
      <input
        type="file"
        accept={accept}
        multiple={multiple}
        onChange={handleFileChange}
        disabled={disabled}
        className="hidden"
        id="file-upload"
      />
      <label htmlFor="file-upload" className="cursor-pointer">
        {children || (
          <div className="space-y-2">
            <Upload className="h-8 w-8 mx-auto text-muted-foreground" />
            <div>
              <p className="text-sm font-medium">
                Drop files here or click to browse
              </p>
              <p className="text-xs text-muted-foreground">
                Max size: {maxSize}MB {accept && `• Accepted: ${accept}`}
              </p>
            </div>
          </div>
        )}
      </label>
    </div>
  )
}

// Multi-select with tags
interface MultiSelectProps {
  options: { value: string; label: string }[]
  selected: string[]
  onChange: (selected: string[]) => void
  placeholder?: string
  disabled?: boolean
}

export function MultiSelect({
  options,
  selected,
  onChange,
  placeholder = "Select options...",
  disabled
}: MultiSelectProps) {
  const [open, setOpen] = useState(false)

  const toggleOption = (value: string) => {
    if (selected.includes(value)) {
      onChange(selected.filter(item => item !== value))
    } else {
      onChange([...selected, value])
    }
  }

  const removeOption = (value: string) => {
    onChange(selected.filter(item => item !== value))
  }

  return (
    <div className="space-y-2">
      <Select open={open} onOpenChange={setOpen}>
        <SelectTrigger disabled={disabled}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem
              key={option.value}
              value={option.value}
              onSelect={() => toggleOption(option.value)}
            >
              <div className="flex items-center gap-2">
                <Checkbox
                  checked={selected.includes(option.value)}
                  onChange={() => toggleOption(option.value)}
                />
                {option.label}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      {selected.length > 0 && (
        <div className="flex flex-wrap gap-1">
          {selected.map((value) => {
            const option = options.find(opt => opt.value === value)
            return (
              <Badge key={value} variant="secondary" className="text-xs">
                {option?.label || value}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="h-auto p-0 ml-1"
                  onClick={() => removeOption(value)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </Badge>
            )
          })}
        </div>
      )}
    </div>
  )
}

// Form section with collapsible content
interface FormSectionProps {
  title: string
  description?: string
  children: ReactNode
  collapsible?: boolean
  defaultOpen?: boolean
}

export function FormSection({
  title,
  description,
  children,
  collapsible = false,
  defaultOpen = true
}: FormSectionProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen)

  return (
    <div className="space-y-4">
      <div
        className={cn(
          "flex items-center justify-between",
          collapsible && "cursor-pointer"
        )}
        onClick={collapsible ? () => setIsOpen(!isOpen) : undefined}
      >
        <div>
          <h3 className="text-lg font-semibold">{title}</h3>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
        {collapsible && (
          <Button variant="ghost" size="sm">
            {isOpen ? "Collapse" : "Expand"}
          </Button>
        )}
      </div>
      
      {isOpen && (
        <div className="space-y-4 pl-4 border-l-2 border-muted">
          {children}
        </div>
      )}
    </div>
  )
}

// Form actions with consistent styling
interface FormActionsProps {
  submitLabel?: string
  cancelLabel?: string
  onCancel?: () => void
  loading?: boolean
  disabled?: boolean
  submitVariant?: 'default' | 'destructive' | 'outline' | 'secondary'
  additionalActions?: ReactNode
}

export function FormActions({
  submitLabel = "Save",
  cancelLabel = "Cancel",
  onCancel,
  loading,
  disabled,
  submitVariant = "default",
  additionalActions
}: FormActionsProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        {additionalActions}
      </div>
      <div className="flex items-center gap-2">
        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={loading}
          >
            {cancelLabel}
          </Button>
        )}
        <Button
          type="submit"
          variant={submitVariant}
          disabled={disabled || loading}
        >
          {loading ? "Saving..." : submitLabel}
        </Button>
      </div>
    </div>
  )
}
