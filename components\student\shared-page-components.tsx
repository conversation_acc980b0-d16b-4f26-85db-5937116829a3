"use client"

import { ReactNode } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Clock,
  Trophy,
  Target,
  TrendingUp,
  TrendingDown,
  Star,
  Play,
  CheckCircle,
  XCircle,
  Award,
  Users,
  BookOpen,
  Calendar,
  Zap,
  Brain,
  Heart,
  Share2,
  Eye,
  ChevronRight,
  Loader2,
  AlertCircle
} from "lucide-react"
import { cn } from "@/lib/utils"
import Link from "next/link"

// ============================================================================
// SHARED STUDENT PAGE COMPONENTS
// ============================================================================

// Student page header
interface StudentPageHeaderProps {
  title: string
  description?: string
  badge?: {
    text: string
    variant?: 'default' | 'secondary' | 'destructive' | 'outline'
  }
  actions?: ReactNode
  stats?: {
    label: string
    value: string | number
    icon?: React.ComponentType<{ className?: string }>
  }[]
}

export function StudentPageHeader({
  title,
  description,
  badge,
  actions,
  stats
}: StudentPageHeaderProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold">{title}</h1>
            {badge && (
              <Badge variant={badge.variant || 'secondary'} className="px-3 py-1">
                {badge.text}
              </Badge>
            )}
          </div>
          {description && (
            <p className="text-muted-foreground mt-1">{description}</p>
          )}
        </div>
        {actions && (
          <div className="flex items-center gap-2">
            {actions}
          </div>
        )}
      </div>
      
      {stats && stats.length > 0 && (
        <div className="flex items-center gap-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <div key={index} className="flex items-center gap-2">
                {Icon && <Icon className="h-4 w-4 text-muted-foreground" />}
                <span className="text-sm text-muted-foreground">{stat.label}:</span>
                <span className="font-medium">{stat.value}</span>
              </div>
            )
          })}
        </div>
      )}
    </div>
  )
}

// Student stats card
interface StudentStatCardProps {
  title: string
  value: string | number
  icon: React.ComponentType<{ className?: string }>
  change?: string
  trend?: 'up' | 'down' | 'neutral'
  description?: string
  color?: string
  onClick?: () => void
}

export function StudentStatCard({ 
  title, 
  value, 
  icon: Icon, 
  change, 
  trend, 
  description, 
  color = "text-primary",
  onClick 
}: StudentStatCardProps) {
  const getTrendColor = () => {
    switch (trend) {
      case 'up': return 'text-green-600'
      case 'down': return 'text-red-600'
      default: return 'text-muted-foreground'
    }
  }

  const TrendIcon = trend === 'up' ? TrendingUp : trend === 'down' ? TrendingDown : null

  const CardComponent = onClick ? 'button' : 'div'

  return (
    <Card className={cn("hover:shadow-md transition-shadow", onClick && "cursor-pointer hover:bg-accent/50")}>
      <CardComponent onClick={onClick} className="w-full text-left">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <p className="text-sm font-medium text-muted-foreground">{title}</p>
              <p className="text-2xl font-bold">{value}</p>
              {change && (
                <div className={cn("flex items-center text-sm", getTrendColor())}>
                  {TrendIcon && <TrendIcon className="h-4 w-4 mr-1" />}
                  {change}
                </div>
              )}
              {description && (
                <p className="text-xs text-muted-foreground">{description}</p>
              )}
            </div>
            <div className={cn("p-3 rounded-full bg-primary/10")}>
              <Icon className={cn("h-6 w-6", color)} />
            </div>
          </div>
        </CardContent>
      </CardComponent>
    </Card>
  )
}

// Quiz card component
interface QuizCardProps {
  quiz: {
    id: string
    title: string
    description?: string
    type: string
    difficulty: string
    timeLimit?: number
    questionCount?: number
    totalAttempts?: number
    isEnrolled?: boolean
    lastAttempt?: {
      score: number
      percentage: number
      completedAt: string
    } | null
    instructor?: {
      name: string
    }
    thumbnail?: string
  }
  onEnroll?: (quizId: string) => void
  onUnenroll?: (quizId: string) => void
  onFavorite?: (quizId: string) => void
  onShare?: (quizId: string) => void
  showActions?: boolean
  variant?: 'grid' | 'list'
}

export function QuizCard({ 
  quiz, 
  onEnroll, 
  onUnenroll, 
  onFavorite, 
  onShare, 
  showActions = true,
  variant = 'grid'
}: QuizCardProps) {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'hard': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'quiz': return BookOpen
      case 'test_series': return Trophy
      case 'daily_practice': return Target
      default: return BookOpen
    }
  }

  const TypeIcon = getTypeIcon(quiz.type)

  if (variant === 'list') {
    return (
      <Card className="hover:shadow-md transition-shadow">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-start gap-4 flex-1">
              <div className="p-2 bg-primary/10 rounded-lg">
                <TypeIcon className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="font-semibold truncate">{quiz.title}</h3>
                  <Badge className={getDifficultyColor(quiz.difficulty)}>
                    {quiz.difficulty}
                  </Badge>
                </div>
                {quiz.description && (
                  <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
                    {quiz.description}
                  </p>
                )}
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  {quiz.timeLimit && (
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {quiz.timeLimit} min
                    </div>
                  )}
                  {quiz.questionCount && (
                    <div className="flex items-center gap-1">
                      <BookOpen className="h-3 w-3" />
                      {quiz.questionCount} questions
                    </div>
                  )}
                  {quiz.totalAttempts && (
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {quiz.totalAttempts} attempts
                    </div>
                  )}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {quiz.lastAttempt && (
                <Badge variant="outline" className="text-xs">
                  {quiz.lastAttempt.percentage}%
                </Badge>
              )}
              {showActions && (
                <div className="flex items-center gap-1">
                  {onFavorite && (
                    <Button variant="ghost" size="sm" onClick={() => onFavorite(quiz.id)}>
                      <Heart className="h-4 w-4" />
                    </Button>
                  )}
                  {onShare && (
                    <Button variant="ghost" size="sm" onClick={() => onShare(quiz.id)}>
                      <Share2 className="h-4 w-4" />
                    </Button>
                  )}
                  <Button asChild>
                    <Link href={`/student/browse/${quiz.id}`}>
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="hover:shadow-md transition-shadow group">
      <CardContent className="p-0">
        {quiz.thumbnail && (
          <div className="aspect-video bg-gradient-to-br from-primary/20 to-primary/5 relative overflow-hidden">
            <img 
              src={quiz.thumbnail} 
              alt={quiz.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute top-2 right-2">
              <Badge className={getDifficultyColor(quiz.difficulty)}>
                {quiz.difficulty}
              </Badge>
            </div>
          </div>
        )}
        
        <div className="p-4 space-y-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              <TypeIcon className="h-4 w-4 text-primary" />
              <Badge variant="outline" className="text-xs">
                {quiz.type.replace('_', ' ')}
              </Badge>
            </div>
            {quiz.isEnrolled && (
              <Badge variant="secondary" className="text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                Enrolled
              </Badge>
            )}
          </div>

          <div>
            <h3 className="font-semibold line-clamp-2 mb-1">{quiz.title}</h3>
            {quiz.description && (
              <p className="text-sm text-muted-foreground line-clamp-2">
                {quiz.description}
              </p>
            )}
          </div>

          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <div className="flex items-center gap-3">
              {quiz.timeLimit && (
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {quiz.timeLimit}m
                </div>
              )}
              {quiz.questionCount && (
                <div className="flex items-center gap-1">
                  <BookOpen className="h-3 w-3" />
                  {quiz.questionCount}
                </div>
              )}
            </div>
            {quiz.instructor && (
              <span>{quiz.instructor.name}</span>
            )}
          </div>

          {quiz.lastAttempt && (
            <div className="flex items-center justify-between p-2 bg-muted/50 rounded">
              <span className="text-xs text-muted-foreground">Last Score:</span>
              <Badge variant="outline">{quiz.lastAttempt.percentage}%</Badge>
            </div>
          )}

          {showActions && (
            <div className="flex items-center justify-between pt-2">
              <div className="flex items-center gap-1">
                {onFavorite && (
                  <Button variant="ghost" size="sm" onClick={() => onFavorite(quiz.id)}>
                    <Heart className="h-4 w-4" />
                  </Button>
                )}
                {onShare && (
                  <Button variant="ghost" size="sm" onClick={() => onShare(quiz.id)}>
                    <Share2 className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <Button asChild size="sm">
                <Link href={`/student/browse/${quiz.id}`}>
                  <Play className="h-4 w-4 mr-2" />
                  Start
                </Link>
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Recent activity item
interface ActivityItemProps {
  activity: {
    id: string
    type: string
    title: string
    description?: string
    timestamp: string
    score?: number
    percentage?: number
    status?: string
  }
  onClick?: () => void
}

export function ActivityItem({ activity, onClick }: ActivityItemProps) {
  const getActivityIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'quiz_completed': return CheckCircle
      case 'quiz_started': return Play
      case 'achievement_earned': return Award
      case 'practice_session': return Target
      default: return BookOpen
    }
  }

  const getStatusColor = (status?: string) => {
    switch (status?.toLowerCase()) {
      case 'completed': return 'text-green-600'
      case 'in_progress': return 'text-yellow-600'
      case 'failed': return 'text-red-600'
      default: return 'text-muted-foreground'
    }
  }

  const ActivityIcon = getActivityIcon(activity.type)

  return (
    <div 
      className={cn(
        "flex items-start gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors",
        onClick && "cursor-pointer"
      )}
      onClick={onClick}
    >
      <div className="p-2 bg-primary/10 rounded-full">
        <ActivityIcon className="h-4 w-4 text-primary" />
      </div>
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <h4 className="font-medium truncate">{activity.title}</h4>
          <span className="text-xs text-muted-foreground">
            {new Date(activity.timestamp).toLocaleDateString()}
          </span>
        </div>
        {activity.description && (
          <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
            {activity.description}
          </p>
        )}
        <div className="flex items-center gap-4 mt-2">
          {activity.percentage !== undefined && (
            <Badge variant="outline" className="text-xs">
              {activity.percentage}%
            </Badge>
          )}
          {activity.status && (
            <span className={cn("text-xs", getStatusColor(activity.status))}>
              {activity.status.replace('_', ' ')}
            </span>
          )}
        </div>
      </div>
    </div>
  )
}

// Loading state for student pages
export function StudentLoadingState({ message = "Loading..." }: { message?: string }) {
  return (
    <div className="flex items-center justify-center py-12">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary mx-auto mb-4" />
        <p className="text-muted-foreground">{message}</p>
      </div>
    </div>
  )
}

// Empty state for student pages
interface StudentEmptyStateProps {
  title: string
  description?: string
  icon?: React.ComponentType<{ className?: string }>
  action?: {
    label: string
    href?: string
    onClick?: () => void
  }
}

export function StudentEmptyState({ title, description, icon: Icon, action }: StudentEmptyStateProps) {
  return (
    <div className="text-center py-12">
      {Icon && <Icon className="h-16 w-16 text-muted-foreground mx-auto mb-4" />}
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      {description && (
        <p className="text-muted-foreground mb-6 max-w-md mx-auto">{description}</p>
      )}
      {action && (
        <Button asChild={!!action.href} onClick={action.onClick}>
          {action.href ? (
            <Link href={action.href}>{action.label}</Link>
          ) : (
            action.label
          )}
        </Button>
      )}
    </div>
  )
}
