// ============================================================================
// CONSOLIDATED SCHEMA LIBRARY
// ============================================================================
// This file provides a centralized export of all validation schemas
// to eliminate redundancy across the application

// Export all common schemas (using explicit exports to avoid conflicts)
export * from "./common"
export * from "./quiz"
export * from "./api"

// Re-export commonly used schemas with convenient names
export {
  // Common schemas
  paginationSchema,
  enhancedPaginationSchema,
  searchSchema,
  idParamSchema,
  userRoleSchema,
  quizTypeSchema,
  difficultySchema,
  questionTypeSchema,
  emailSchema,
  nameSchema,
  descriptionSchema,
  urlSchema,
  tagsArraySchema,
  scoreSchema,
  timeLimitSchema,
  maxAttemptsSchema,
  booleanFlagSchema,
  dateTimeSchema,
  themeSchema,
  languageSchema,
  
  // Utility functions
  createPaginatedResponseSchema,
  createFilteredQuerySchema,
  createCRUDSchemas,
  
  // Common composed schemas
  commonQuerySchemas,
  commonFieldSchemas,
  commonEnumSchemas
} from "./common"

export {
  // Question schemas
  questionSchema,
  mcqQuestionSchema,
  trueFalseQuestionSchema,
  shortAnswerQuestionSchema,
  matchingQuestionSchema,
  questionsArraySchema,
  flexibleQuestionsSchema,
  
  // Quiz schemas
  baseQuizSchema,
  createQuizSchema,
  updateQuizSchema,
  quizQuerySchema,
  
  // Quiz attempt schemas (moved to student schemas)
  
  // Quiz scheduling schemas
  scheduleQuizSchema,
  updateScheduledQuizSchema,
  
  // Quiz operations
  quizAnalyticsQuerySchema,
  quizExportSchema,
  duplicateQuizSchema,
  bulkQuizOperationSchema,
  quizStatsSchema,
  
  // All quiz schemas object
  quizSchemas
} from "./quiz"

export {
  // User schemas (basic ones, admin-specific moved to admin section)
  baseUserSchema,
  userProfileSchema,
  userQuerySchema,

  // Authentication schemas
  loginSchema,
  registerSchema,
  changePasswordSchema,
  resetPasswordSchema,
  confirmResetPasswordSchema,

  // User preferences
  updatePreferencesSchema,

  // User achievements and activity
  achievementSchema,
  userAchievementSchema,
  userActivitySchema,
  userActivityQuerySchema,

  // User statistics and leaderboards
  userStatsSchema,

  // All user schemas object
  userSchemas
} from "./user"

export {
  // API response schemas
  apiSuccessSchema,
  apiErrorSchema,
  apiResponseSchema,
  paginatedApiResponseSchema,
  
  // File operation schemas
  fileUploadSchema,
  uploadedFileSchema,
  multipleFileUploadSchema,
  
  // Bulk operation schemas
  bulkOperationRequestSchema,
  bulkOperationResultSchema,
  
  // Search schemas
  searchRequestSchema,
  searchResultSchema,
  
  // Export/Import schemas
  exportRequestSchema,
  exportJobSchema,
  importRequestSchema,
  importJobSchema,
  
  // Analytics schemas
  analyticsQuerySchema,
  analyticsResultSchema,
  
  // Webhook schemas
  webhookEventSchema,
  webhookConfigSchema,
  
  // System schemas
  rateLimitSchema,
  apiVersionSchema,
  healthCheckSchema,
  
  // All API schemas object
  apiSchemas
} from "./api"

export {
  // Admin user management
  adminUserQuerySchema,
  createUserSchema,
  updateUserSchema,
  bulkUserOperationSchema,

  // Admin file management
  adminFileQuerySchema,
  uploadFileSchema,
  updateFileSchema,

  // Admin notifications
  adminNotificationQuerySchema,
  createNotificationSchema,
  updateNotificationSchema,

  // Admin settings
  systemSettingsSchema,
  securitySettingsSchema,
  emailSettingsSchema,
  settingsUpdateSchema,

  // Admin analytics
  adminAnalyticsQuerySchema,
  studentAnalyticsQuerySchema,

  // Admin exports
  pdfExportQuerySchema,
  bulkExportSchema,

  // Admin AI features
  quizCreationRequestSchema,
  imageGenerationSchema,
  tagSuggestionSchema,
  testSeriesSchema,

  // Admin dashboard
  dashboardStatsQuerySchema,
  notificationStatsQuerySchema,

  // All admin schemas object
  adminSchemas
} from "./admin"

export {
  // Student quiz browsing
  studentQuizQuerySchema,
  startQuizAttemptSchema,
  submitAnswerSchema,
  submitQuizAttemptSchema,

  // Student profile
  studentProfileUpdateSchema,
  notificationPreferencesSchema,
  dashboardCustomizationSchema,

  // Student history and analytics
  studentHistoryQuerySchema,
  studentAnalyticsQuerySchema as studentAnalyticsQuery,

  // Student practice
  startPracticeSessionSchema,
  practiceSessionQuerySchema,

  // Student favorites
  manageFavoriteSchema,
  favoritesQuerySchema,

  // Student achievements
  achievementQuerySchema as studentAchievementQuerySchema,
  goalSettingSchema,
  goalUpdateSchema,

  // Student leaderboard
  leaderboardQuerySchema as studentLeaderboardQuerySchema,

  // Student study plans
  studyPlanQuerySchema,
  createStudyPlanSchema,
  updateStudyPlanSchema,

  // Student discussions
  discussionQuerySchema,
  createDiscussionSchema,
  replyDiscussionSchema,

  // Student certificates
  certificateRequestSchema,

  // All student schemas object
  studentSchemas
} from "./student"

// Legacy compatibility exports
// These maintain backward compatibility with existing code
export {
  // From the original schemas.ts file
  questionSchema as Question,
  questionsArraySchema as questionsSchema
} from "./quiz"

// Schema validation utilities
export const validateSchema = <T>(schema: any, data: unknown): T => {
  return schema.parse(data)
}

export const validateSchemaAsync = async <T>(schema: any, data: unknown): Promise<T> => {
  return schema.parseAsync(data)
}

export const isValidSchema = (schema: any, data: unknown): boolean => {
  try {
    schema.parse(data)
    return true
  } catch {
    return false
  }
}

// Schema composition helpers
export const extendSchema = <T extends Record<string, any>, U extends Record<string, any>>(
  baseSchema: T,
  extension: U
) => {
  return baseSchema.extend(extension)
}

export const mergeSchemas = (...schemas: any[]) => {
  return schemas.reduce((acc, schema) => acc.merge(schema))
}

// Type inference helpers
export type InferSchemaType<T> = T extends { parse: (data: any) => infer U } ? U : never

// Common validation patterns
export const createValidationMiddleware = (schema: any) => {
  return (data: unknown) => {
    try {
      return { success: true, data: schema.parse(data), errors: null }
    } catch (error: any) {
      return {
        success: false,
        data: null,
        errors: error.errors || [{ message: error.message }]
      }
    }
  }
}

// Common API response types
export type APISuccessResponse<T = any> = {
  success: true
  data: T
  message?: string
  timestamp: string
}

export type APIErrorResponse = {
  success: false
  error: string
  code?: string
  details?: any
  timestamp: string
}
