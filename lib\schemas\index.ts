// ============================================================================
// CONSOLIDATED SCHEMA LIBRARY
// ============================================================================
// This file provides a centralized export of all validation schemas
// to eliminate redundancy across the application

// Export all common schemas
export * from "./common"
export * from "./quiz"
export * from "./user"
export * from "./api"

// Re-export commonly used schemas with convenient names
export {
  // Common schemas
  paginationSchema,
  enhancedPaginationSchema,
  searchSchema,
  idParamSchema,
  userRoleSchema,
  quizTypeSchema,
  difficultySchema,
  questionTypeSchema,
  emailSchema,
  nameSchema,
  descriptionSchema,
  urlSchema,
  tagsArraySchema,
  scoreSchema,
  timeLimitSchema,
  maxAttemptsSchema,
  booleanFlagSchema,
  dateTimeSchema,
  themeSchema,
  languageSchema,
  
  // Utility functions
  createPaginatedResponseSchema,
  createFilteredQuerySchema,
  createCRUDSchemas,
  
  // Common composed schemas
  commonQuerySchemas,
  commonFieldSchemas,
  commonEnumSchemas
} from "./common"

export {
  // Question schemas
  questionSchema,
  mcqQuestionSchema,
  trueFalseQuestionSchema,
  shortAnswerQuestionSchema,
  matchingQuestionSchema,
  questionsArraySchema,
  flexibleQuestionsSchema,
  
  // Quiz schemas
  baseQuizSchema,
  createQuizSchema,
  updateQuizSchema,
  quizQuerySchema,
  
  // Quiz attempt schemas
  startQuizAttemptSchema,
  submitAnswerSchema,
  submitQuizAttemptSchema,
  
  // Quiz scheduling schemas
  scheduleQuizSchema,
  updateScheduledQuizSchema,
  
  // Quiz operations
  quizAnalyticsQuerySchema,
  quizExportSchema,
  duplicateQuizSchema,
  bulkQuizOperationSchema,
  quizStatsSchema,
  
  // All quiz schemas object
  quizSchemas
} from "./quiz"

export {
  // User schemas
  baseUserSchema,
  createUserSchema,
  updateUserSchema,
  userProfileSchema,
  userQuerySchema,
  
  // Authentication schemas
  loginSchema,
  registerSchema,
  changePasswordSchema,
  resetPasswordSchema,
  confirmResetPasswordSchema,
  
  // User preferences
  updatePreferencesSchema,
  
  // User achievements and activity
  achievementSchema,
  userAchievementSchema,
  userActivitySchema,
  userActivityQuerySchema,
  
  // User statistics and leaderboards
  userStatsSchema,
  leaderboardQuerySchema,
  leaderboardEntrySchema,
  
  // User operations
  bulkUserOperationSchema,
  
  // User notifications
  notificationSchema,
  createNotificationSchema,
  notificationQuerySchema,
  
  // All user schemas object
  userSchemas
} from "./user"

export {
  // API response schemas
  apiSuccessSchema,
  apiErrorSchema,
  apiResponseSchema,
  paginatedApiResponseSchema,
  
  // File operation schemas
  fileUploadSchema,
  uploadedFileSchema,
  multipleFileUploadSchema,
  
  // Bulk operation schemas
  bulkOperationRequestSchema,
  bulkOperationResultSchema,
  
  // Search schemas
  searchRequestSchema,
  searchResultSchema,
  
  // Export/Import schemas
  exportRequestSchema,
  exportJobSchema,
  importRequestSchema,
  importJobSchema,
  
  // Analytics schemas
  analyticsQuerySchema,
  analyticsResultSchema,
  
  // Webhook schemas
  webhookEventSchema,
  webhookConfigSchema,
  
  // System schemas
  rateLimitSchema,
  apiVersionSchema,
  healthCheckSchema,
  
  // All API schemas object
  apiSchemas
} from "./api"

// Legacy compatibility exports
// These maintain backward compatibility with existing code
export {
  // From the original schemas.ts file
  questionSchema as Question,
  questionsArraySchema as questionsSchema,
  flexibleQuestionsSchema
} from "./quiz"

// Commonly used schema combinations for convenience
export const commonAPISchemas = {
  // Standard CRUD operations
  create: createQuizSchema,
  update: updateQuizSchema,
  query: quizQuerySchema,
  
  // User operations
  createUser: createUserSchema,
  updateUser: updateUserSchema,
  userQuery: userQuerySchema,
  
  // Common API patterns
  pagination: paginationSchema,
  search: searchSchema,
  bulkOperation: bulkOperationRequestSchema
}

// Schema validation utilities
export const validateSchema = <T>(schema: any, data: unknown): T => {
  return schema.parse(data)
}

export const validateSchemaAsync = async <T>(schema: any, data: unknown): Promise<T> => {
  return schema.parseAsync(data)
}

export const isValidSchema = (schema: any, data: unknown): boolean => {
  try {
    schema.parse(data)
    return true
  } catch {
    return false
  }
}

// Schema composition helpers
export const extendSchema = <T extends Record<string, any>, U extends Record<string, any>>(
  baseSchema: T,
  extension: U
) => {
  return baseSchema.extend(extension)
}

export const mergeSchemas = (...schemas: any[]) => {
  return schemas.reduce((acc, schema) => acc.merge(schema))
}

// Type inference helpers
export type InferSchemaType<T> = T extends { parse: (data: any) => infer U } ? U : never

// Common validation patterns
export const createValidationMiddleware = (schema: any) => {
  return (data: unknown) => {
    try {
      return { success: true, data: schema.parse(data), errors: null }
    } catch (error: any) {
      return { 
        success: false, 
        data: null, 
        errors: error.errors || [{ message: error.message }] 
      }
    }
  }
}

// Export types for TypeScript
export type PaginationQuery = ReturnType<typeof paginationSchema.parse>
export type SearchQuery = ReturnType<typeof searchSchema.parse>
export type QuizQuery = ReturnType<typeof quizQuerySchema.parse>
export type UserQuery = ReturnType<typeof userQuerySchema.parse>
export type CreateQuizData = ReturnType<typeof createQuizSchema.parse>
export type UpdateQuizData = ReturnType<typeof updateQuizSchema.parse>
export type CreateUserData = ReturnType<typeof createUserSchema.parse>
export type UpdateUserData = ReturnType<typeof updateUserSchema.parse>
export type QuestionData = ReturnType<typeof questionSchema.parse>
export type APISuccessResponse<T = any> = {
  success: true
  data: T
  message?: string
  timestamp: string
}
export type APIErrorResponse = {
  success: false
  error: string
  code?: string
  details?: any
  timestamp: string
}
