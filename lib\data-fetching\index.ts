import { prisma } from '@/lib/prisma'
import { APIResponse } from '@/lib/api-middleware'

// ============================================================================
// SHARED DATA FETCHING UTILITIES
// ============================================================================

// Common pagination interface
export interface PaginationOptions {
  page: number
  limit: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginationResult<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// Generic paginated query builder
export async function createPaginatedQuery<T>(
  model: any,
  options: PaginationOptions,
  whereClause: any = {},
  includeClause: any = {},
  selectClause?: any
): Promise<PaginationResult<T>> {
  const { page, limit, search, sortBy = 'createdAt', sortOrder = 'desc' } = options
  const skip = (page - 1) * limit

  // Build search conditions
  let searchConditions = {}
  if (search) {
    searchConditions = {
      OR: [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { name: { contains: search, mode: 'insensitive' } }
      ]
    }
  }

  const finalWhere = {
    ...whereClause,
    ...searchConditions
  }

  // Execute queries in parallel
  const [data, total] = await Promise.all([
    model.findMany({
      where: finalWhere,
      include: includeClause,
      select: selectClause,
      orderBy: { [sortBy]: sortOrder },
      skip,
      take: limit
    }),
    model.count({ where: finalWhere })
  ])

  const totalPages = Math.ceil(total / limit)
  const hasNext = page < totalPages
  const hasPrev = page > 1

  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext,
      hasPrev
    }
  }
}

// User-related data fetching
export class UserDataService {
  static async getUserWithStats(userId: string) {
    return await prisma.user.findUnique({
      where: { id: userId },
      include: {
        _count: {
          select: {
            quizAttempts: { where: { isCompleted: true } },
            enrollments: true
          }
        }
      }
    })
  }

  static async getUserQuizHistory(userId: string, options: PaginationOptions) {
    return await createPaginatedQuery(
      prisma.quizAttempt,
      options,
      { userId, isCompleted: true },
      {
        quiz: {
          select: {
            id: true,
            title: true,
            type: true,
            difficulty: true,
            points: true
          }
        }
      }
    )
  }

  static async getUserAchievements(userId: string) {
    // This would typically fetch from a user_achievements table
    // For now, return empty array as placeholder
    return []
  }
}

// Quiz-related data fetching
export class QuizDataService {
  static async getPublishedQuizzes(options: PaginationOptions & {
    type?: string
    difficulty?: string
    subjectId?: string
    chapterId?: string
    topicId?: string
  }) {
    const { type, difficulty, subjectId, chapterId, topicId, ...paginationOptions } = options
    
    let whereClause: any = { isPublished: true }
    
    if (type) whereClause.type = type
    if (difficulty) whereClause.difficulty = difficulty
    if (subjectId) whereClause.subjectId = subjectId
    if (chapterId) whereClause.chapterId = chapterId
    if (topicId) whereClause.topicId = topicId

    return await createPaginatedQuery(
      prisma.quiz,
      paginationOptions,
      whereClause,
      {
        creator: {
          select: { name: true, email: true }
        },
        subject: {
          select: { name: true }
        },
        chapter: {
          select: { name: true }
        },
        topic: {
          select: { name: true }
        },
        _count: {
          select: { questions: true, attempts: true }
        }
      }
    )
  }

  static async getQuizWithQuestions(quizId: string, includeAnswers: boolean = false) {
    const selectClause = includeAnswers ? undefined : {
      id: true,
      type: true,
      text: true,
      options: true,
      points: true,
      order: true,
      image: true
    }

    return await prisma.quiz.findUnique({
      where: { id: quizId },
      include: {
        questions: {
          select: selectClause,
          orderBy: { order: 'asc' }
        },
        creator: {
          select: { name: true, email: true }
        }
      }
    })
  }

  static async getUserQuizAttempt(userId: string, quizId: string, attemptId?: string) {
    const whereClause: any = {
      userId,
      quizId
    }

    if (attemptId) {
      whereClause.id = attemptId
    } else {
      whereClause.isCompleted = false
    }

    return await prisma.quizAttempt.findFirst({
      where: whereClause,
      include: {
        quiz: {
          select: {
            id: true,
            title: true,
            timeLimit: true,
            maxAttempts: true
          }
        },
        answers: true
      },
      orderBy: { startedAt: 'desc' }
    })
  }
}

// Analytics data fetching
export class AnalyticsDataService {
  static async getUserAnalytics(userId: string, period: string = '30d') {
    const dateRange = this.getDateRange(period)
    
    const [attempts, user] = await Promise.all([
      prisma.quizAttempt.findMany({
        where: {
          userId,
          startedAt: {
            gte: dateRange.start,
            lte: dateRange.end
          }
        },
        include: {
          quiz: {
            select: {
              difficulty: true,
              type: true,
              subject: { select: { name: true } }
            }
          }
        }
      }),
      prisma.user.findUnique({
        where: { id: userId },
        select: {
          totalPoints: true,
          averageScore: true,
          streak: true,
          longestStreak: true
        }
      })
    ])

    return {
      user,
      attempts,
      dateRange,
      summary: {
        totalAttempts: attempts.length,
        completedAttempts: attempts.filter(a => a.isCompleted).length,
        averageScore: attempts.length > 0 
          ? attempts.reduce((sum, a) => sum + (a.percentage || 0), 0) / attempts.length 
          : 0
      }
    }
  }

  static async getSystemAnalytics(period: string = '30d') {
    const dateRange = this.getDateRange(period)
    
    const [totalUsers, totalQuizzes, totalAttempts, averageScore] = await Promise.all([
      prisma.user.count(),
      prisma.quiz.count({ where: { isPublished: true } }),
      prisma.quizAttempt.count({
        where: {
          startedAt: {
            gte: dateRange.start,
            lte: dateRange.end
          }
        }
      }),
      prisma.quizAttempt.aggregate({
        _avg: { percentage: true },
        where: {
          isCompleted: true,
          startedAt: {
            gte: dateRange.start,
            lte: dateRange.end
          }
        }
      })
    ])

    return {
      totalUsers,
      totalQuizzes,
      totalAttempts,
      averageScore: averageScore._avg.percentage || 0,
      dateRange
    }
  }

  private static getDateRange(period: string) {
    const end = new Date()
    const start = new Date()

    switch (period) {
      case '7d':
        start.setDate(end.getDate() - 7)
        break
      case '30d':
        start.setDate(end.getDate() - 30)
        break
      case '90d':
        start.setDate(end.getDate() - 90)
        break
      case '1y':
        start.setFullYear(end.getFullYear() - 1)
        break
      default:
        start.setDate(end.getDate() - 30)
    }

    return { start, end }
  }
}

// Category data fetching
export class CategoryDataService {
  static async getActiveSubjects() {
    return await prisma.subject.findMany({
      where: { isActive: true },
      include: {
        chapters: {
          where: { isActive: true },
          include: {
            topics: {
              where: { isActive: true },
              orderBy: { order: 'asc' }
            }
          },
          orderBy: { order: 'asc' }
        },
        _count: {
          select: {
            quizzes: { where: { isPublished: true } },
            chapters: { where: { isActive: true } }
          }
        }
      },
      orderBy: { name: 'asc' }
    })
  }

  static async getSubjectWithDetails(subjectId: string) {
    return await prisma.subject.findUnique({
      where: { id: subjectId, isActive: true },
      include: {
        chapters: {
          where: { isActive: true },
          include: {
            topics: {
              where: { isActive: true },
              orderBy: { order: 'asc' }
            }
          },
          orderBy: { order: 'asc' }
        }
      }
    })
  }
}

// Response helpers
export class ResponseHelpers {
  static paginated<T>(data: T[], pagination: any, message: string = 'Data retrieved successfully') {
    return APIResponse.paginated(data, pagination, message)
  }

  static success<T>(data: T, message: string = 'Operation successful') {
    return APIResponse.success(data, message)
  }

  static error(message: string, status: number = 500, code?: string) {
    return APIResponse.error(message, status, code)
  }
}

// Export all services
export {
  UserDataService,
  QuizDataService,
  AnalyticsDataService,
  CategoryDataService,
  ResponseHelpers
}
