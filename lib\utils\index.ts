// ============================================================================
// CONSOLIDATED UTILITY FUNCTIONS
// ============================================================================
// This file provides centralized utility functions to eliminate redundancy
// across the application

// Re-export existing utils
export * from "../utils"

// Date and time utilities
export const dateUtils = {
  formatDate: (date: Date | string, format: 'short' | 'long' | 'relative' = 'short') => {
    const d = new Date(date)
    
    switch (format) {
      case 'short':
        return d.toLocaleDateString()
      case 'long':
        return d.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      case 'relative':
        return formatRelativeTime(d)
      default:
        return d.toLocaleDateString()
    }
  },

  formatTime: (date: Date | string) => {
    return new Date(date).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    })
  },

  formatDuration: (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}m`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  },

  isExpired: (date: Date | string) => {
    return new Date(date) < new Date()
  },

  timeUntil: (date: Date | string) => {
    const now = new Date()
    const target = new Date(date)
    const diff = target.getTime() - now.getTime()
    
    if (diff <= 0) return 'Expired'
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    
    if (days > 0) return `${days}d ${hours}h`
    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  }
}

function formatRelativeTime(date: Date): string {
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const seconds = Math.floor(diff / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`
  if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`
  if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`
  return 'Just now'
}

// File and size utilities
export const fileUtils = {
  formatFileSize: (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  getFileExtension: (filename: string) => {
    return filename.split('.').pop()?.toLowerCase() || ''
  },

  isImageFile: (filename: string) => {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']
    return imageExtensions.includes(fileUtils.getFileExtension(filename))
  },

  isPdfFile: (filename: string) => {
    return fileUtils.getFileExtension(filename) === 'pdf'
  },

  generateFileName: (originalName: string, prefix?: string) => {
    const timestamp = Date.now()
    const extension = fileUtils.getFileExtension(originalName)
    const baseName = originalName.replace(/\.[^/.]+$/, "")
    const sanitizedName = baseName.replace(/[^a-zA-Z0-9]/g, '_')
    
    return prefix 
      ? `${prefix}_${sanitizedName}_${timestamp}.${extension}`
      : `${sanitizedName}_${timestamp}.${extension}`
  }
}

// Number and score utilities
export const numberUtils = {
  formatScore: (score: number, total?: number) => {
    if (total) {
      return `${score}/${total}`
    }
    return score.toString()
  },

  formatPercentage: (value: number, decimals: number = 1) => {
    return `${value.toFixed(decimals)}%`
  },

  formatNumber: (num: number) => {
    return num.toLocaleString()
  },

  calculatePercentage: (value: number, total: number) => {
    if (total === 0) return 0
    return Math.round((value / total) * 100)
  },

  clamp: (value: number, min: number, max: number) => {
    return Math.min(Math.max(value, min), max)
  },

  roundToDecimal: (value: number, decimals: number = 2) => {
    return Math.round(value * Math.pow(10, decimals)) / Math.pow(10, decimals)
  }
}

// String utilities
export const stringUtils = {
  truncate: (str: string, length: number, suffix: string = '...') => {
    if (str.length <= length) return str
    return str.substring(0, length - suffix.length) + suffix
  },

  capitalize: (str: string) => {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
  },

  capitalizeWords: (str: string) => {
    return str.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    )
  },

  slugify: (str: string) => {
    return str
      .toLowerCase()
      .replace(/[^\w ]+/g, '')
      .replace(/ +/g, '-')
  },

  extractInitials: (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2)
  },

  generateId: (length: number = 8) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let result = ''
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }
}

// Array utilities
export const arrayUtils = {
  shuffle: <T>(array: T[]): T[] => {
    const shuffled = [...array]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    return shuffled
  },

  chunk: <T>(array: T[], size: number): T[][] => {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  },

  unique: <T>(array: T[]): T[] => {
    return [...new Set(array)]
  },

  groupBy: <T, K extends keyof T>(array: T[], key: K): Record<string, T[]> => {
    return array.reduce((groups, item) => {
      const group = String(item[key])
      groups[group] = groups[group] || []
      groups[group].push(item)
      return groups
    }, {} as Record<string, T[]>)
  },

  sortBy: <T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] => {
    return [...array].sort((a, b) => {
      const aVal = a[key]
      const bVal = b[key]
      
      if (aVal < bVal) return direction === 'asc' ? -1 : 1
      if (aVal > bVal) return direction === 'asc' ? 1 : -1
      return 0
    })
  }
}

// Validation utilities
export const validationUtils = {
  isEmail: (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  isUrl: (url: string) => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },

  isStrongPassword: (password: string) => {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special char
    const strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    return strongPasswordRegex.test(password)
  },

  sanitizeHtml: (html: string) => {
    // Basic HTML sanitization - in production, use a proper library like DOMPurify
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
  }
}

// Local storage utilities (client-side only)
export const storageUtils = {
  set: (key: string, value: any) => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(key, JSON.stringify(value))
      } catch (error) {
        console.warn('Failed to save to localStorage:', error)
      }
    }
  },

  get: <T>(key: string, defaultValue?: T): T | null => {
    if (typeof window !== 'undefined') {
      try {
        const item = localStorage.getItem(key)
        return item ? JSON.parse(item) : defaultValue || null
      } catch (error) {
        console.warn('Failed to read from localStorage:', error)
        return defaultValue || null
      }
    }
    return defaultValue || null
  },

  remove: (key: string) => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(key)
    }
  },

  clear: () => {
    if (typeof window !== 'undefined') {
      localStorage.clear()
    }
  }
}

// Quiz-specific utilities
export const quizUtils = {
  calculateScore: (correctAnswers: number, totalQuestions: number) => {
    if (totalQuestions === 0) return 0
    return numberUtils.calculatePercentage(correctAnswers, totalQuestions)
  },

  getGradeFromScore: (score: number) => {
    if (score >= 90) return 'A'
    if (score >= 80) return 'B'
    if (score >= 70) return 'C'
    if (score >= 60) return 'D'
    return 'F'
  },

  getDifficultyColor: (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'text-green-600'
      case 'medium': return 'text-yellow-600'
      case 'hard': return 'text-red-600'
      default: return 'text-gray-600'
    }
  },

  getTypeIcon: (type: string) => {
    switch (type.toLowerCase()) {
      case 'quiz': return '📝'
      case 'test_series': return '📚'
      case 'daily_practice': return '🎯'
      default: return '❓'
    }
  },

  shuffleQuestions: <T>(questions: T[]): T[] => {
    return arrayUtils.shuffle(questions)
  },

  calculateTimeSpent: (startTime: Date, endTime: Date) => {
    const diff = endTime.getTime() - startTime.getTime()
    return Math.floor(diff / 1000 / 60) // minutes
  }
}

// Export all utilities as a single object for convenience
export const utils = {
  date: dateUtils,
  file: fileUtils,
  number: numberUtils,
  string: stringUtils,
  array: arrayUtils,
  validation: validationUtils,
  storage: storageUtils,
  quiz: quizUtils
}
