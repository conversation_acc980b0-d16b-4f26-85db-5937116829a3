import { z } from "zod"
import {
  nameSchema,
  emailSchema,
  userRoleSchema,
  descriptionSchema,
  urlSchema,
  booleanFlagSchema,
  createFilteredQuerySchema,
  dateTimeSchema,
  scoreSchema
} from "./common"

// ============================================================================
// USER-RELATED SCHEMAS
// ============================================================================

// Base user schema
export const baseUserSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  role: userRoleSchema,
  bio: descriptionSchema,
  avatar: urlSchema.optional(),
  isActive: booleanFlagSchema.default(true),
  emailVerified: booleanFlagSchema.default(false)
})

// Create user schema
export const createUserSchema = baseUserSchema.omit({ isActive: true, emailVerified: true })

// Update user schema (all fields optional)
export const updateUserSchema = baseUserSchema.partial()

// User profile schema (for student profiles)
export const userProfileSchema = baseUserSchema.extend({
  preferences: z.object({
    theme: z.enum(['light', 'dark', 'system']).default('system'),
    language: z.enum(['en', 'hi']).default('en'),
    notifications: z.object({
      email: booleanFlagSchema.default(true),
      push: booleanFlagSchema.default(true),
      quiz_reminders: booleanFlagSchema.default(true),
      achievement_alerts: booleanFlagSchema.default(true)
    }).optional()
  }).optional(),
  stats: z.object({
    totalQuizzes: z.number().default(0),
    completedQuizzes: z.number().default(0),
    averageScore: z.number().default(0),
    totalPoints: z.number().default(0),
    streak: z.number().default(0),
    level: z.number().default(1),
    rank: z.number().optional()
  }).optional()
})

// User query schema with specific filters
export const userQuerySchema = createFilteredQuerySchema({
  role: userRoleSchema.optional(),
  isActive: z.enum(['true', 'false']).optional(),
  emailVerified: z.enum(['true', 'false']).optional(),
  lastActiveFrom: dateTimeSchema.optional(),
  lastActiveTo: dateTimeSchema.optional(),
  sortBy: z.enum(['name', 'email', 'createdAt', 'lastActive']).optional().default('createdAt')
})

// User authentication schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, "Password is required")
})

export const registerSchema = z.object({
  name: nameSchema,
  email: emailSchema,
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string(),
  role: userRoleSchema.optional().default('STUDENT')
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(8, "New password must be at least 8 characters"),
  confirmPassword: z.string()
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

export const resetPasswordSchema = z.object({
  email: emailSchema
})

export const confirmResetPasswordSchema = z.object({
  token: z.string().min(1, "Reset token is required"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string()
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"]
})

// User preferences schema
export const updatePreferencesSchema = z.object({
  theme: z.enum(['light', 'dark', 'system']).optional(),
  language: z.enum(['en', 'hi']).optional(),
  notifications: z.object({
    email: booleanFlagSchema.optional(),
    push: booleanFlagSchema.optional(),
    quiz_reminders: booleanFlagSchema.optional(),
    achievement_alerts: booleanFlagSchema.optional()
  }).optional()
})

// User achievement schemas
export const achievementSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  icon: z.string(),
  category: z.enum(['quiz', 'streak', 'score', 'participation', 'special']),
  points: z.number().min(0),
  rarity: z.enum(['common', 'rare', 'epic', 'legendary']),
  requirements: z.object({
    type: z.string(),
    value: z.number(),
    comparison: z.enum(['gte', 'lte', 'eq'])
  })
})

export const userAchievementSchema = z.object({
  achievementId: z.string(),
  unlockedAt: dateTimeSchema,
  progress: z.number().min(0).max(100).optional()
})

// User activity schemas
export const userActivitySchema = z.object({
  type: z.enum(['quiz_started', 'quiz_completed', 'achievement_unlocked', 'login', 'profile_updated']),
  metadata: z.record(z.any()).optional(),
  timestamp: dateTimeSchema
})

export const userActivityQuerySchema = createFilteredQuerySchema({
  userId: z.string().optional(),
  type: z.enum(['quiz_started', 'quiz_completed', 'achievement_unlocked', 'login', 'profile_updated']).optional(),
  dateFrom: dateTimeSchema.optional(),
  dateTo: dateTimeSchema.optional()
})

// User statistics schemas
export const userStatsSchema = z.object({
  totalUsers: z.number(),
  activeUsers: z.number(),
  newUsersToday: z.number(),
  newUsersThisWeek: z.number(),
  newUsersThisMonth: z.number(),
  usersByRole: z.object({
    STUDENT: z.number(),
    ADMIN: z.number()
  }),
  topPerformers: z.array(z.object({
    id: z.string(),
    name: z.string(),
    totalPoints: z.number(),
    averageScore: z.number(),
    completedQuizzes: z.number()
  }))
})

// User leaderboard schemas
export const leaderboardQuerySchema = z.object({
  period: z.enum(['daily', 'weekly', 'monthly', 'all_time']).default('all_time'),
  category: z.enum(['points', 'quizzes', 'average_score', 'streak']).default('points'),
  limit: z.number().min(1).max(100).default(10)
})

export const leaderboardEntrySchema = z.object({
  rank: z.number(),
  user: z.object({
    id: z.string(),
    name: z.string(),
    avatar: urlSchema.optional()
  }),
  score: z.number(),
  change: z.number().optional() // Change in rank from previous period
})

// User bulk operations schema
export const bulkUserOperationSchema = z.object({
  userIds: z.array(z.string()).min(1, "At least one user ID required"),
  operation: z.enum(['activate', 'deactivate', 'delete', 'change_role']),
  newRole: userRoleSchema.optional(), // Required for change_role operation
  confirmDeletion: z.boolean().optional() // Required for delete operation
}).refine(data => {
  if (data.operation === 'change_role') {
    return data.newRole !== undefined
  }
  if (data.operation === 'delete') {
    return data.confirmDeletion === true
  }
  return true
}, {
  message: "Invalid operation parameters"
})

// User notification schemas
export const notificationSchema = z.object({
  id: z.string(),
  type: z.enum(['info', 'success', 'warning', 'error']),
  title: z.string(),
  message: z.string(),
  read: booleanFlagSchema.default(false),
  createdAt: dateTimeSchema,
  expiresAt: dateTimeSchema.optional(),
  actionUrl: urlSchema.optional(),
  actionText: z.string().optional()
})

export const createNotificationSchema = notificationSchema.omit({ 
  id: true, 
  read: true, 
  createdAt: true 
})

export const notificationQuerySchema = createFilteredQuerySchema({
  read: z.enum(['true', 'false']).optional(),
  type: z.enum(['info', 'success', 'warning', 'error']).optional()
})

// Export all user-related schemas
export const userSchemas = {
  // Basic user schemas
  baseUser: baseUserSchema,
  createUser: createUserSchema,
  updateUser: updateUserSchema,
  userProfile: userProfileSchema,
  userQuery: userQuerySchema,
  
  // Authentication schemas
  login: loginSchema,
  register: registerSchema,
  changePassword: changePasswordSchema,
  resetPassword: resetPasswordSchema,
  confirmResetPassword: confirmResetPasswordSchema,
  
  // Preferences and settings
  updatePreferences: updatePreferencesSchema,
  
  // Achievements and activity
  achievement: achievementSchema,
  userAchievement: userAchievementSchema,
  userActivity: userActivitySchema,
  userActivityQuery: userActivityQuerySchema,
  
  // Statistics and leaderboards
  userStats: userStatsSchema,
  leaderboardQuery: leaderboardQuerySchema,
  leaderboardEntry: leaderboardEntrySchema,
  
  // Bulk operations
  bulkOperation: bulkUserOperationSchema,
  
  // Notifications
  notification: notificationSchema,
  createNotification: createNotificationSchema,
  notificationQuery: notificationQuerySchema
}
