import { screen, waitFor, fireEvent } from '@testing-library/react'
import { renderWithProviders, mockFetch, testData, mockAdminSession, cleanup } from '../utils/test-helpers'
import AdminDashboard from '@/app/admin/page'

// ============================================================================
// ADMIN DASHBOARD INTEGRATION TESTS
// ============================================================================

describe('Admin Dashboard Integration', () => {
  let restoreFetch: () => void

  beforeEach(() => {
    // Mock API responses
    restoreFetch = mockFetch({
      'GET /api/admin/dashboard': {
        status: 200,
        data: {
          success: true,
          data: {
            stats: {
              totalUsers: 150,
              activeUsers: 45,
              totalQuizzes: 25,
              totalAttempts: 500,
              averageScore: 78.5
            },
            recentActivity: [
              {
                id: '1',
                type: 'user_registered',
                title: 'New user registered',
                description: 'John Doe joined the platform',
                timestamp: '2024-01-15T10:30:00Z',
                user: { name: 'John Doe', email: '<EMAIL>' }
              }
            ],
            trends: {
              usersGrowth: 12.5,
              quizzesGrowth: 8.3,
              attemptsGrowth: 15.7,
              scoreImprovement: 3.2
            }
          }
        }
      }
    })
  })

  afterEach(() => {
    restoreFetch()
    cleanup.completeCleanup()
  })

  it('should render dashboard with all sections', async () => {
    renderWithProviders(<AdminDashboard />, { session: mockAdminSession })

    // Check for loading state initially
    expect(screen.getByText(/loading/i)).toBeInTheDocument()

    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
    })

    // Check for stats cards
    expect(screen.getByText('150')).toBeInTheDocument() // Total users
    expect(screen.getByText('45')).toBeInTheDocument() // Active users
    expect(screen.getByText('25')).toBeInTheDocument() // Total quizzes
    expect(screen.getByText('500')).toBeInTheDocument() // Total attempts

    // Check for recent activity
    expect(screen.getByText('Recent Activity')).toBeInTheDocument()
    expect(screen.getByText('New user registered')).toBeInTheDocument()
  })

  it('should handle refresh functionality', async () => {
    renderWithProviders(<AdminDashboard />, { session: mockAdminSession })

    await waitFor(() => {
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
    })

    // Find and click refresh button
    const refreshButton = screen.getByRole('button', { name: /refresh/i })
    fireEvent.click(refreshButton)

    // Should show loading state again
    await waitFor(() => {
      expect(screen.getByText(/loading/i)).toBeInTheDocument()
    })

    // Should reload data
    await waitFor(() => {
      expect(screen.getByText('150')).toBeInTheDocument()
    })
  })

  it('should handle error states gracefully', async () => {
    // Mock error response
    const errorRestore = mockFetch({
      'GET /api/admin/dashboard': {
        status: 500,
        data: { success: false, error: 'Internal server error' }
      }
    })

    renderWithProviders(<AdminDashboard />, { session: mockAdminSession })

    await waitFor(() => {
      expect(screen.getByText(/error/i)).toBeInTheDocument()
    })

    // Should show retry button
    const retryButton = screen.getByRole('button', { name: /try again/i })
    expect(retryButton).toBeInTheDocument()

    errorRestore()
  })

  it('should be accessible', async () => {
    renderWithProviders(<AdminDashboard />, { session: mockAdminSession })

    await waitFor(() => {
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
    })

    // Check for proper heading structure
    const mainHeading = screen.getByRole('heading', { level: 1 })
    expect(mainHeading).toBeInTheDocument()

    // Check for proper button labels
    const buttons = screen.getAllByRole('button')
    buttons.forEach(button => {
      expect(button).toHaveAccessibleName()
    })

    // Check for proper link labels
    const links = screen.getAllByRole('link')
    links.forEach(link => {
      expect(link).toHaveAccessibleName()
    })
  })

  it('should handle navigation correctly', async () => {
    renderWithProviders(<AdminDashboard />, { session: mockAdminSession })

    await waitFor(() => {
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
    })

    // Test navigation to users page
    const usersLink = screen.getByRole('link', { name: /users/i })
    expect(usersLink).toHaveAttribute('href', '/admin/users')

    // Test navigation to quizzes page
    const quizzesLink = screen.getByRole('link', { name: /quizzes/i })
    expect(quizzesLink).toHaveAttribute('href', '/admin/quizzes')
  })

  it('should update stats in real-time', async () => {
    renderWithProviders(<AdminDashboard />, { session: mockAdminSession })

    await waitFor(() => {
      expect(screen.getByText('150')).toBeInTheDocument()
    })

    // Simulate real-time update
    const updatedRestore = mockFetch({
      'GET /api/admin/dashboard': {
        status: 200,
        data: {
          success: true,
          data: {
            stats: {
              totalUsers: 151, // Updated count
              activeUsers: 46,
              totalQuizzes: 25,
              totalAttempts: 502,
              averageScore: 78.7
            },
            recentActivity: [],
            trends: {
              usersGrowth: 13.0,
              quizzesGrowth: 8.3,
              attemptsGrowth: 16.0,
              scoreImprovement: 3.5
            }
          }
        }
      }
    })

    // Trigger refresh
    const refreshButton = screen.getByRole('button', { name: /refresh/i })
    fireEvent.click(refreshButton)

    await waitFor(() => {
      expect(screen.getByText('151')).toBeInTheDocument()
    })

    updatedRestore()
  })

  it('should handle permissions correctly', async () => {
    // Test with student session (should redirect or show error)
    renderWithProviders(<AdminDashboard />, { 
      session: { 
        user: { 
          id: 'student-1', 
          role: 'STUDENT', 
          name: 'Student User', 
          email: '<EMAIL>' 
        } 
      } 
    })

    await waitFor(() => {
      // Should show unauthorized message or redirect
      expect(screen.getByText(/unauthorized|access denied/i)).toBeInTheDocument()
    })
  })

  it('should perform well with large datasets', async () => {
    // Mock large dataset
    const largeDataRestore = mockFetch({
      'GET /api/admin/dashboard': {
        status: 200,
        data: {
          success: true,
          data: {
            stats: {
              totalUsers: 10000,
              activeUsers: 2500,
              totalQuizzes: 500,
              totalAttempts: 50000,
              averageScore: 78.5
            },
            recentActivity: Array.from({ length: 100 }, (_, i) => ({
              id: `activity-${i}`,
              type: 'user_registered',
              title: `Activity ${i}`,
              description: `Description ${i}`,
              timestamp: new Date().toISOString(),
              user: { name: `User ${i}`, email: `user${i}@example.com` }
            })),
            trends: {
              usersGrowth: 12.5,
              quizzesGrowth: 8.3,
              attemptsGrowth: 15.7,
              scoreImprovement: 3.2
            }
          }
        }
      }
    })

    const startTime = performance.now()
    
    renderWithProviders(<AdminDashboard />, { session: mockAdminSession })

    await waitFor(() => {
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
    })

    const endTime = performance.now()
    const renderTime = endTime - startTime

    // Should render within reasonable time (less than 2 seconds)
    expect(renderTime).toBeLessThan(2000)

    largeDataRestore()
  })

  it('should handle concurrent updates correctly', async () => {
    renderWithProviders(<AdminDashboard />, { session: mockAdminSession })

    await waitFor(() => {
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
    })

    // Simulate multiple rapid refresh clicks
    const refreshButton = screen.getByRole('button', { name: /refresh/i })
    
    fireEvent.click(refreshButton)
    fireEvent.click(refreshButton)
    fireEvent.click(refreshButton)

    // Should handle concurrent requests gracefully
    await waitFor(() => {
      expect(screen.getByText('150')).toBeInTheDocument()
    })

    // Should not show multiple loading states
    const loadingElements = screen.queryAllByText(/loading/i)
    expect(loadingElements.length).toBeLessThanOrEqual(1)
  })
})
