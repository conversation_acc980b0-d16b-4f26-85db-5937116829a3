import { render, screen } from '@testing-library/react'
import { performanceUtils, renderWithProviders, testData, mockSession } from '../utils/test-helpers'
import { StudentStatCard, QuizCard, DataTable } from '@/components/shared'

// ============================================================================
// COMPONENT PERFORMANCE TESTS
// ============================================================================

describe('Component Performance Tests', () => {
  beforeEach(() => {
    // Clear performance marks
    if (performance.clearMarks) {
      performance.clearMarks()
    }
    if (performance.clearMeasures) {
      performance.clearMeasures()
    }
  })

  describe('StudentStatCard Performance', () => {
    it('should render quickly with basic props', async () => {
      const renderTime = await performanceUtils.measureRenderTime(() => {
        render(
          <StudentStatCard
            title="Test Stat"
            value="100"
            icon={() => <div>Icon</div>}
          />
        )
      })

      // Should render in less than 50ms
      expect(renderTime).toBeLessThan(50)
    })

    it('should handle multiple instances efficiently', async () => {
      const stats = Array.from({ length: 20 }, (_, i) => ({
        title: `Stat ${i}`,
        value: `${i * 10}`,
        icon: () => <div>Icon {i}</div>
      }))

      const renderTime = await performanceUtils.measureRenderTime(() => {
        render(
          <div>
            {stats.map((stat, index) => (
              <StudentStatCard key={index} {...stat} />
            ))}
          </div>
        )
      })

      // Should render 20 cards in less than 200ms
      expect(renderTime).toBeLessThan(200)
    })

    it('should not cause memory leaks', async () => {
      const memoryStats = await performanceUtils.testMemoryLeaks(
        <StudentStatCard
          title="Memory Test"
          value="123"
          icon={() => <div>Icon</div>}
        />,
        50
      )

      // Memory increase should be minimal (less than 1MB per iteration)
      expect(memoryStats.averagePerIteration).toBeLessThan(1024 * 1024)
    })
  })

  describe('QuizCard Performance', () => {
    const mockQuiz = testData.quiz({
      title: 'Performance Test Quiz',
      description: 'A quiz for testing performance',
      questions: Array.from({ length: 50 }, (_, i) => testData.question({
        id: `q-${i}`,
        text: `Question ${i}`
      }))
    })

    it('should render quiz card efficiently', async () => {
      const renderTime = await performanceUtils.measureRenderTime(() => {
        render(<QuizCard quiz={mockQuiz} />)
      })

      // Should render in less than 100ms even with many questions
      expect(renderTime).toBeLessThan(100)
    })

    it('should handle grid layout efficiently', async () => {
      const quizzes = Array.from({ length: 12 }, (_, i) => 
        testData.quiz({ id: `quiz-${i}`, title: `Quiz ${i}` })
      )

      const renderTime = await performanceUtils.measureRenderTime(() => {
        render(
          <div className="grid grid-cols-3 gap-4">
            {quizzes.map(quiz => (
              <QuizCard key={quiz.id} quiz={quiz} />
            ))}
          </div>
        )
      })

      // Should render 12 quiz cards in grid in less than 300ms
      expect(renderTime).toBeLessThan(300)
    })

    it('should optimize re-renders', async () => {
      const { rerender } = render(<QuizCard quiz={mockQuiz} />)

      const rerenderTime = await performanceUtils.measureRenderTime(() => {
        rerender(<QuizCard quiz={mockQuiz} />)
      })

      // Re-renders should be faster than initial render
      expect(rerenderTime).toBeLessThan(20)
    })
  })

  describe('DataTable Performance', () => {
    const generateLargeDataset = (size: number) => 
      Array.from({ length: size }, (_, i) => ({
        id: `item-${i}`,
        name: `Item ${i}`,
        email: `item${i}@example.com`,
        role: i % 2 === 0 ? 'ADMIN' : 'STUDENT',
        createdAt: new Date().toISOString()
      }))

    it('should handle large datasets efficiently', async () => {
      const data = generateLargeDataset(1000)
      const columns = [
        { key: 'name', label: 'Name' },
        { key: 'email', label: 'Email' },
        { key: 'role', label: 'Role' },
        { key: 'createdAt', label: 'Created' }
      ]

      const renderTime = await performanceUtils.measureRenderTime(() => {
        render(
          <DataTable
            data={data}
            columns={columns}
            pagination={{ page: 1, limit: 50, total: 1000, pages: 20 }}
          />
        )
      })

      // Should render large table in less than 500ms
      expect(renderTime).toBeLessThan(500)
    })

    it('should virtualize large lists', async () => {
      const data = generateLargeDataset(10000)
      const columns = [
        { key: 'name', label: 'Name' },
        { key: 'email', label: 'Email' }
      ]

      render(
        <DataTable
          data={data}
          columns={columns}
          pagination={{ page: 1, limit: 100, total: 10000, pages: 100 }}
          virtualized={true}
        />
      )

      // Should only render visible rows in DOM
      const rows = screen.getAllByRole('row')
      // Header + visible rows (should be much less than 10000)
      expect(rows.length).toBeLessThan(200)
    })

    it('should handle sorting efficiently', async () => {
      const data = generateLargeDataset(5000)
      const columns = [
        { key: 'name', label: 'Name', sortable: true },
        { key: 'email', label: 'Email', sortable: true }
      ]

      const { container } = render(
        <DataTable
          data={data}
          columns={columns}
          pagination={{ page: 1, limit: 100, total: 5000, pages: 50 }}
        />
      )

      const nameHeader = screen.getByText('Name')
      
      const sortTime = await performanceUtils.measureRenderTime(() => {
        nameHeader.click()
      })

      // Sorting should complete in less than 100ms
      expect(sortTime).toBeLessThan(100)
    })
  })

  describe('Page Load Performance', () => {
    it('should measure Time to Interactive (TTI)', async () => {
      performance.mark('page-start')
      
      renderWithProviders(
        <div>
          <h1>Test Page</h1>
          <StudentStatCard title="Stat 1" value="100" icon={() => <div>Icon</div>} />
          <StudentStatCard title="Stat 2" value="200" icon={() => <div>Icon</div>} />
          <StudentStatCard title="Stat 3" value="300" icon={() => <div>Icon</div>} />
        </div>,
        { session: mockSession }
      )

      performance.mark('page-interactive')
      performance.measure('page-load-time', 'page-start', 'page-interactive')

      const measures = performance.getEntriesByName('page-load-time')
      const loadTime = measures[0]?.duration || 0

      // Page should be interactive in less than 1 second
      expect(loadTime).toBeLessThan(1000)
    })

    it('should measure First Contentful Paint (FCP)', async () => {
      performance.mark('render-start')
      
      render(<div>First content</div>)
      
      performance.mark('first-content')
      performance.measure('fcp-time', 'render-start', 'first-content')

      const measures = performance.getEntriesByName('fcp-time')
      const fcpTime = measures[0]?.duration || 0

      // First content should appear in less than 100ms
      expect(fcpTime).toBeLessThan(100)
    })
  })

  describe('Bundle Size Impact', () => {
    it('should not significantly increase bundle size', () => {
      // This would typically be measured by build tools
      // For now, we can check that components are tree-shakeable
      
      // Import only specific components
      const { StudentStatCard } = require('@/components/shared')
      expect(StudentStatCard).toBeDefined()
      
      // Verify that unused components are not included
      // This would be verified by bundle analysis tools in CI/CD
    })

    it('should lazy load heavy components', async () => {
      // Test that heavy components are loaded on demand
      const LazyComponent = React.lazy(() => 
        import('@/components/shared/dashboard-components').then(module => ({
          default: module.ActivityFeed
        }))
      )

      const { container } = render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <LazyComponent title="Test" activities={[]} />
        </React.Suspense>
      )

      // Should show loading state initially
      expect(screen.getByText('Loading...')).toBeInTheDocument()

      // Should load component
      await waitFor(() => {
        expect(screen.getByText('Test')).toBeInTheDocument()
      })
    })
  })

  describe('Memory Usage', () => {
    it('should clean up event listeners', async () => {
      const addEventListenerSpy = jest.spyOn(window, 'addEventListener')
      const removeEventListenerSpy = jest.spyOn(window, 'removeEventListener')

      const { unmount } = render(
        <StudentStatCard
          title="Event Test"
          value="100"
          icon={() => <div>Icon</div>}
          onClick={() => {}}
        />
      )

      const addedListeners = addEventListenerSpy.mock.calls.length
      
      unmount()

      const removedListeners = removeEventListenerSpy.mock.calls.length

      // Should clean up all event listeners
      expect(removedListeners).toBeGreaterThanOrEqual(addedListeners)

      addEventListenerSpy.mockRestore()
      removeEventListenerSpy.mockRestore()
    })

    it('should not retain references after unmount', async () => {
      let componentRef: any = null

      const TestComponent = () => {
        componentRef = React.useRef()
        return <div ref={componentRef}>Test</div>
      }

      const { unmount } = render(<TestComponent />)
      
      expect(componentRef.current).toBeTruthy()
      
      unmount()
      
      // Force garbage collection if available
      if ((global as any).gc) {
        (global as any).gc()
      }

      // Reference should be cleared (this is a simplified test)
      // In real scenarios, you'd use more sophisticated memory profiling
      expect(componentRef.current).toBeTruthy() // Still exists until GC
    })
  })
})
