// ============================================================================
// SHARED HOOKS LIBRARY
// ============================================================================
// This file provides centralized custom hooks to eliminate redundancy
// across admin and student components

import { useState, useEffect, useCallback, useRef } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'

// Common API hook for data fetching
export function useApi<T>(
  url: string,
  options?: {
    initialData?: T
    dependencies?: any[]
    enabled?: boolean
    onSuccess?: (data: T) => void
    onError?: (error: Error) => void
  }
) {
  const [data, setData] = useState<T | null>(options?.initialData || null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const fetchData = useCallback(async () => {
    if (options?.enabled === false) return

    setLoading(true)
    setError(null)

    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        setData(result.data)
        options?.onSuccess?.(result.data)
      } else {
        throw new Error(result.error || 'API request failed')
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      setError(error)
      options?.onError?.(error)
    } finally {
      setLoading(false)
    }
  }, [url, options?.enabled])

  useEffect(() => {
    fetchData()
  }, [fetchData, ...(options?.dependencies || [])])

  const refetch = useCallback(() => {
    fetchData()
  }, [fetchData])

  return { data, loading, error, refetch }
}

// Paginated data hook
export function usePaginatedApi<T>(
  baseUrl: string,
  options?: {
    initialPage?: number
    pageSize?: number
    dependencies?: any[]
    enabled?: boolean
  }
) {
  const [page, setPage] = useState(options?.initialPage || 1)
  const [pageSize] = useState(options?.pageSize || 20)
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [pagination, setPagination] = useState({
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })

  const fetchData = useCallback(async () => {
    if (options?.enabled === false) return

    setLoading(true)
    setError(null)

    try {
      const url = `${baseUrl}?page=${page}&limit=${pageSize}`
      const response = await fetch(url)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        setData(result.data)
        setPagination(result.pagination)
      } else {
        throw new Error(result.error || 'API request failed')
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      setError(error)
    } finally {
      setLoading(false)
    }
  }, [baseUrl, page, pageSize, options?.enabled])

  useEffect(() => {
    fetchData()
  }, [fetchData, ...(options?.dependencies || [])])

  const nextPage = useCallback(() => {
    if (pagination.hasNext) {
      setPage(prev => prev + 1)
    }
  }, [pagination.hasNext])

  const prevPage = useCallback(() => {
    if (pagination.hasPrev) {
      setPage(prev => prev - 1)
    }
  }, [pagination.hasPrev])

  const goToPage = useCallback((newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      setPage(newPage)
    }
  }, [pagination.totalPages])

  const refetch = useCallback(() => {
    fetchData()
  }, [fetchData])

  return {
    data,
    loading,
    error,
    pagination: { ...pagination, page, pageSize },
    nextPage,
    prevPage,
    goToPage,
    refetch
  }
}

// Form submission hook
export function useFormSubmit<T>(
  url: string,
  options?: {
    method?: 'POST' | 'PUT' | 'PATCH' | 'DELETE'
    onSuccess?: (data: any) => void
    onError?: (error: Error) => void
    showToast?: boolean
  }
) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const submit = useCallback(async (data: T) => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch(url, {
        method: options?.method || 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()

      if (result.success) {
        if (options?.showToast !== false) {
          toast.success(result.message || 'Operation completed successfully')
        }
        options?.onSuccess?.(result.data)
        return result.data
      } else {
        throw new Error(result.error || 'Operation failed')
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Unknown error')
      setError(error)
      
      if (options?.showToast !== false) {
        toast.error(error.message)
      }
      
      options?.onError?.(error)
      throw error
    } finally {
      setLoading(false)
    }
  }, [url, options])

  return { submit, loading, error }
}

// Local storage hook
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void] {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue
    }

    try {
      const item = window.localStorage.getItem(key)
      return item ? JSON.parse(item) : initialValue
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error)
      return initialValue
    }
  })

  const setValue = useCallback((value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value
      setStoredValue(valueToStore)
      
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore))
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error)
    }
  }, [key, storedValue])

  return [storedValue, setValue]
}

// Debounced value hook
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// User role and permissions hook
export function useUserRole() {
  const { data: session } = useSession()

  const isAdmin = session?.user?.role === 'ADMIN'
  const isStudent = session?.user?.role === 'STUDENT'
  const isAuthenticated = !!session?.user

  const hasPermission = useCallback((permission: string) => {
    // Define permission logic here
    if (isAdmin) return true
    
    // Add specific student permissions
    const studentPermissions = [
      'quiz:take',
      'quiz:view',
      'profile:edit',
      'history:view'
    ]
    
    return isStudent && studentPermissions.includes(permission)
  }, [isAdmin, isStudent])

  return {
    user: session?.user,
    isAdmin,
    isStudent,
    isAuthenticated,
    hasPermission,
    role: session?.user?.role
  }
}

// Timer hook for quiz functionality
export function useTimer(
  initialTime: number,
  options?: {
    onTick?: (timeLeft: number) => void
    onComplete?: () => void
    autoStart?: boolean
  }
) {
  const [timeLeft, setTimeLeft] = useState(initialTime)
  const [isRunning, setIsRunning] = useState(options?.autoStart || false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  const start = useCallback(() => {
    setIsRunning(true)
  }, [])

  const pause = useCallback(() => {
    setIsRunning(false)
  }, [])

  const reset = useCallback(() => {
    setTimeLeft(initialTime)
    setIsRunning(false)
  }, [initialTime])

  const stop = useCallback(() => {
    setIsRunning(false)
    setTimeLeft(0)
  }, [])

  useEffect(() => {
    if (isRunning && timeLeft > 0) {
      intervalRef.current = setInterval(() => {
        setTimeLeft(prev => {
          const newTime = prev - 1
          options?.onTick?.(newTime)
          
          if (newTime <= 0) {
            setIsRunning(false)
            options?.onComplete?.()
          }
          
          return newTime
        })
      }, 1000)
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [isRunning, timeLeft, options])

  const formatTime = useCallback((seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }, [])

  return {
    timeLeft,
    isRunning,
    start,
    pause,
    reset,
    stop,
    formatTime: formatTime(timeLeft)
  }
}

// Search hook with debouncing
export function useSearch<T>(
  searchFunction: (query: string) => Promise<T[]>,
  options?: {
    debounceMs?: number
    minQueryLength?: number
  }
) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<T[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const debouncedQuery = useDebounce(query, options?.debounceMs || 300)

  useEffect(() => {
    const performSearch = async () => {
      if (debouncedQuery.length < (options?.minQueryLength || 1)) {
        setResults([])
        return
      }

      setLoading(true)
      setError(null)

      try {
        const searchResults = await searchFunction(debouncedQuery)
        setResults(searchResults)
      } catch (err) {
        const error = err instanceof Error ? err : new Error('Search failed')
        setError(error)
        setResults([])
      } finally {
        setLoading(false)
      }
    }

    performSearch()
  }, [debouncedQuery, searchFunction, options?.minQueryLength])

  const clearSearch = useCallback(() => {
    setQuery('')
    setResults([])
    setError(null)
  }, [])

  return {
    query,
    setQuery,
    results,
    loading,
    error,
    clearSearch
  }
}
