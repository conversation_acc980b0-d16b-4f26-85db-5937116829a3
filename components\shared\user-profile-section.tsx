"use client"

import { useState } from "react"
import { useSession, signOut } from "next-auth/react"
import { useTheme } from "next-themes"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  LogOut,
  Moon,
  Sun,
  ChevronDown,
  User,
  Settings
} from "lucide-react"
import { cn } from "@/lib/utils"

interface UserData {
  name: string
  email: string
  image?: string
  level?: number
  totalPoints?: number
  role?: string
}

interface UserProfileSectionProps {
  userData?: UserData | null
  loading?: boolean
  showStats?: boolean
  showThemeToggle?: boolean
  showDropdown?: boolean
  compact?: boolean
  className?: string
  profileHref?: string
  settingsHref?: string
}

export function UserProfileSection({
  userData,
  loading = false,
  showStats = false,
  showThemeToggle = true,
  showDropdown = true,
  compact = false,
  className,
  profileHref,
  settingsHref
}: UserProfileSectionProps) {
  const { data: session } = useSession()
  const { theme, setTheme } = useTheme()

  // Use session data as fallback
  const displayData = userData || {
    name: session?.user?.name || "User",
    email: session?.user?.email || "",
    image: session?.user?.image,
    role: session?.user?.role
  }

  const initials = displayData.name
    ?.split(' ')
    .map(n => n[0])
    .join('')
    .toUpperCase() || "U"

  if (loading) {
    return (
      <div className={cn("flex items-center gap-3 p-2", className)}>
        <div className="h-10 w-10 bg-muted rounded-full animate-pulse" />
        {!compact && (
          <div className="flex-1">
            <div className="h-4 bg-muted rounded w-24 mb-1 animate-pulse" />
            <div className="h-3 bg-muted rounded w-16 animate-pulse" />
          </div>
        )}
      </div>
    )
  }

  if (!showDropdown) {
    return (
      <div className={cn("flex items-center gap-3 p-2", className)}>
        <Avatar className="h-10 w-10">
          <AvatarImage src={displayData.image || ""} />
          <AvatarFallback>{initials}</AvatarFallback>
        </Avatar>
        {!compact && (
          <div className="flex-1">
            <div className="text-sm font-medium">{displayData.name}</div>
            {showStats && userData?.level && userData?.totalPoints ? (
              <div className="text-xs text-muted-foreground">
                Level {userData.level} • {userData.totalPoints.toLocaleString()} pts
              </div>
            ) : (
              <div className="text-xs text-muted-foreground">{displayData.email}</div>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          className={cn(
            "flex items-center gap-3 p-2 h-auto justify-start hover:bg-accent",
            className
          )}
        >
          <Avatar className="h-10 w-10">
            <AvatarImage src={displayData.image || ""} />
            <AvatarFallback>{initials}</AvatarFallback>
          </Avatar>
          {!compact && (
            <div className="flex-1 text-left">
              <div className="text-sm font-medium">{displayData.name}</div>
              {showStats && userData?.level && userData?.totalPoints ? (
                <div className="text-xs text-muted-foreground">
                  Level {userData.level} • {userData.totalPoints.toLocaleString()} pts
                </div>
              ) : (
                <div className="text-xs text-muted-foreground">{displayData.email}</div>
              )}
            </div>
          )}
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium">{displayData.name}</p>
            <p className="text-xs text-muted-foreground">{displayData.email}</p>
            {displayData.role && (
              <p className="text-xs text-muted-foreground capitalize">
                {displayData.role.toLowerCase()}
              </p>
            )}
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuSeparator />
        
        {profileHref && (
          <DropdownMenuItem asChild>
            <a href={profileHref} className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Profile
            </a>
          </DropdownMenuItem>
        )}
        
        {settingsHref && (
          <DropdownMenuItem asChild>
            <a href={settingsHref} className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </a>
          </DropdownMenuItem>
        )}
        
        {showThemeToggle && (
          <DropdownMenuItem
            onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            className="flex items-center gap-2"
          >
            {theme === 'dark' ? (
              <>
                <Sun className="h-4 w-4" />
                Light Mode
              </>
            ) : (
              <>
                <Moon className="h-4 w-4" />
                Dark Mode
              </>
            )}
          </DropdownMenuItem>
        )}
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem
          onClick={() => signOut()}
          className="flex items-center gap-2 text-destructive focus:text-destructive"
        >
          <LogOut className="h-4 w-4" />
          Sign Out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
