import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { ReactElement } from 'react'
import { SessionProvider } from 'next-auth/react'
import { AppProvider } from '@/contexts/app-context'
import { DataCacheProvider } from '@/contexts/data-cache-context'
import { Toaster } from 'sonner'

// ============================================================================
// COMPREHENSIVE TESTING UTILITIES
// ============================================================================

// Mock session data
export const mockSession = {
  user: {
    id: 'test-user-id',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'STUDENT'
  },
  expires: '2024-12-31'
}

export const mockAdminSession = {
  user: {
    id: 'admin-user-id',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'ADMIN'
  },
  expires: '2024-12-31'
}

// Test wrapper with all providers
interface TestWrapperProps {
  children: ReactElement
  session?: any
  initialCache?: Record<string, any>
}

export function TestWrapper({ children, session = mockSession, initialCache = {} }: TestWrapperProps) {
  return (
    <SessionProvider session={session}>
      <AppProvider>
        <DataCacheProvider>
          {children}
          <Toaster />
        </DataCacheProvider>
      </AppProvider>
    </SessionProvider>
  )
}

// Custom render function with providers
export function renderWithProviders(
  ui: ReactElement,
  options: {
    session?: any
    initialCache?: Record<string, any>
  } = {}
) {
  return render(ui, {
    wrapper: ({ children }) => (
      <TestWrapper session={options.session} initialCache={options.initialCache}>
        {children}
      </TestWrapper>
    )
  })
}

// Mock API responses
export const mockApiResponse = {
  success: (data: any, message = 'Success') => ({
    success: true,
    data,
    message
  }),
  
  error: (message: string, code?: string) => ({
    success: false,
    error: message,
    code
  }),
  
  paginated: (data: any[], pagination: any) => ({
    success: true,
    data,
    pagination
  })
}

// Mock fetch function
export function mockFetch(responses: Record<string, any>) {
  const originalFetch = global.fetch
  
  global.fetch = jest.fn((url: string, options?: any) => {
    const method = options?.method || 'GET'
    const key = `${method} ${url}`
    
    if (responses[key]) {
      const response = responses[key]
      return Promise.resolve({
        ok: response.status < 400,
        status: response.status || 200,
        json: () => Promise.resolve(response.data),
        text: () => Promise.resolve(JSON.stringify(response.data))
      } as Response)
    }
    
    // Default 404 response
    return Promise.resolve({
      ok: false,
      status: 404,
      json: () => Promise.resolve({ error: 'Not found' }),
      text: () => Promise.resolve('Not found')
    } as Response)
  })
  
  return () => {
    global.fetch = originalFetch
  }
}

// Test data generators
export const testData = {
  user: (overrides: any = {}) => ({
    id: 'user-1',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'STUDENT',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    ...overrides
  }),
  
  quiz: (overrides: any = {}) => ({
    id: 'quiz-1',
    title: 'Test Quiz',
    description: 'A test quiz',
    type: 'QUIZ',
    difficulty: 'MEDIUM',
    timeLimit: 30,
    isPublished: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    questions: [],
    ...overrides
  }),
  
  question: (overrides: any = {}) => ({
    id: 'question-1',
    text: 'What is 2 + 2?',
    type: 'MULTIPLE_CHOICE',
    options: ['2', '3', '4', '5'],
    correctAnswer: '4',
    points: 1,
    order: 1,
    ...overrides
  }),
  
  quizAttempt: (overrides: any = {}) => ({
    id: 'attempt-1',
    userId: 'user-1',
    quizId: 'quiz-1',
    score: 80,
    percentage: 80,
    isCompleted: true,
    startedAt: '2024-01-01T00:00:00Z',
    completedAt: '2024-01-01T00:30:00Z',
    ...overrides
  })
}

// Common test scenarios
export const testScenarios = {
  // Loading state tests
  async testLoadingState(getByTestId: any, loadingTestId: string) {
    expect(getByTestId(loadingTestId)).toBeInTheDocument()
  },
  
  // Error state tests
  async testErrorState(getByText: any, errorMessage: string) {
    await waitFor(() => {
      expect(getByText(errorMessage)).toBeInTheDocument()
    })
  },
  
  // Form submission tests
  async testFormSubmission(
    form: HTMLElement,
    submitButton: HTMLElement,
    expectedData: any
  ) {
    fireEvent.submit(form)
    
    await waitFor(() => {
      expect(submitButton).toBeDisabled()
    })
    
    // Add assertions for API calls
  },
  
  // Pagination tests
  async testPagination(container: HTMLElement) {
    const nextButton = screen.getByRole('button', { name: /next/i })
    const prevButton = screen.getByRole('button', { name: /previous/i })
    
    expect(prevButton).toBeDisabled()
    
    fireEvent.click(nextButton)
    
    await waitFor(() => {
      expect(prevButton).not.toBeDisabled()
    })
  },
  
  // Search functionality tests
  async testSearch(searchInput: HTMLElement, searchTerm: string) {
    fireEvent.change(searchInput, { target: { value: searchTerm } })
    
    await waitFor(() => {
      // Add assertions for search results
    }, { timeout: 1000 })
  }
}

// Performance testing utilities
export const performanceUtils = {
  // Measure component render time
  measureRenderTime: async (renderFn: () => void) => {
    const start = performance.now()
    renderFn()
    const end = performance.now()
    return end - start
  },
  
  // Test for memory leaks
  testMemoryLeaks: async (component: ReactElement, iterations = 100) => {
    const initialMemory = (performance as any).memory?.usedJSHeapSize || 0
    
    for (let i = 0; i < iterations; i++) {
      const { unmount } = render(component)
      unmount()
    }
    
    // Force garbage collection if available
    if ((global as any).gc) {
      (global as any).gc()
    }
    
    const finalMemory = (performance as any).memory?.usedJSHeapSize || 0
    const memoryIncrease = finalMemory - initialMemory
    
    return {
      initialMemory,
      finalMemory,
      memoryIncrease,
      averagePerIteration: memoryIncrease / iterations
    }
  }
}

// Accessibility testing utilities
export const a11yUtils = {
  // Test keyboard navigation
  testKeyboardNavigation: async (container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    expect(focusableElements.length).toBeGreaterThan(0)
    
    // Test tab navigation
    for (let i = 0; i < focusableElements.length; i++) {
      fireEvent.keyDown(document.activeElement || document.body, { key: 'Tab' })
      await waitFor(() => {
        expect(document.activeElement).toBe(focusableElements[i])
      })
    }
  },
  
  // Test ARIA attributes
  testAriaAttributes: (element: HTMLElement, expectedAttributes: Record<string, string>) => {
    Object.entries(expectedAttributes).forEach(([attr, value]) => {
      expect(element).toHaveAttribute(attr, value)
    })
  },
  
  // Test screen reader announcements
  testScreenReaderAnnouncements: async (container: HTMLElement) => {
    const liveRegions = container.querySelectorAll('[aria-live]')
    expect(liveRegions.length).toBeGreaterThan(0)
    
    liveRegions.forEach(region => {
      expect(region).toHaveAttribute('aria-live')
    })
  }
}

// Integration test utilities
export const integrationUtils = {
  // Test full user flow
  testUserFlow: async (steps: Array<() => Promise<void>>) => {
    for (const step of steps) {
      await step()
    }
  },
  
  // Test API integration
  testApiIntegration: async (
    endpoint: string,
    method: string,
    data?: any,
    expectedResponse?: any
  ) => {
    const response = await fetch(endpoint, {
      method,
      headers: { 'Content-Type': 'application/json' },
      body: data ? JSON.stringify(data) : undefined
    })
    
    expect(response.ok).toBe(true)
    
    if (expectedResponse) {
      const responseData = await response.json()
      expect(responseData).toMatchObject(expectedResponse)
    }
  },
  
  // Test real-time features
  testRealtimeFeatures: async (socketUrl: string, events: string[]) => {
    // Mock WebSocket for testing
    const mockSocket = {
      send: jest.fn(),
      close: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn()
    }
    
    // Test event handling
    events.forEach(event => {
      expect(mockSocket.addEventListener).toHaveBeenCalledWith(event, expect.any(Function))
    })
  }
}

// Custom matchers for Jest
export const customMatchers = {
  toBeAccessible: (element: HTMLElement) => {
    const hasAriaLabel = element.hasAttribute('aria-label') || element.hasAttribute('aria-labelledby')
    const hasRole = element.hasAttribute('role')
    const isSemanticElement = ['button', 'input', 'select', 'textarea', 'a'].includes(element.tagName.toLowerCase())
    
    return {
      pass: hasAriaLabel || hasRole || isSemanticElement,
      message: () => 'Element should be accessible with proper ARIA attributes or semantic HTML'
    }
  },
  
  toHaveValidationError: (form: HTMLElement, fieldName: string) => {
    const field = form.querySelector(`[name="${fieldName}"]`)
    const errorElement = form.querySelector(`[data-testid="${fieldName}-error"]`)
    
    return {
      pass: !!field && !!errorElement,
      message: () => `Field ${fieldName} should have a validation error`
    }
  }
}

// Test cleanup utilities
export const cleanup = {
  // Clear all mocks
  clearAllMocks: () => {
    jest.clearAllMocks()
    jest.clearAllTimers()
  },
  
  // Reset DOM
  resetDOM: () => {
    document.body.innerHTML = ''
  },
  
  // Clear local storage
  clearStorage: () => {
    localStorage.clear()
    sessionStorage.clear()
  },
  
  // Reset fetch mocks
  resetFetch: () => {
    if (jest.isMockFunction(global.fetch)) {
      (global.fetch as jest.Mock).mockReset()
    }
  },

  // Complete cleanup for test suites
  completeCleanup: () => {
    cleanup.clearAllMocks()
    cleanup.resetDOM()
    cleanup.clearStorage()
    cleanup.resetFetch()
  }
}
