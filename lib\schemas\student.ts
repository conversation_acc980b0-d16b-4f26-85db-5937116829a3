import { z } from "zod"
import {
  nameSchema,
  emailSchema,
  descriptionSchema,
  paginationSchema,
  enhancedPaginationSchema,
  dateTimeSchema,
  booleanFlagSchema,
  urlSchema,
  scoreSchema,
  createFilteredQuerySchema,
  quizTypeSchema,
  difficultySchema,
  questionTypeSchema
} from "./common"

// ============================================================================
// STUDENT-SPECIFIC SCHEMAS
// ============================================================================

// Student quiz browsing and filtering
export const studentQuizQuerySchema = enhancedPaginationSchema.extend({
  type: quizTypeSchema.optional(),
  difficulty: difficultySchema.optional(),
  category: z.string().optional(),
  subjectId: z.string().optional(),
  chapterId: z.string().optional(),
  topicId: z.string().optional(),
  enrolled: z.enum(['true', 'false']).optional(),
  completed: z.enum(['true', 'false']).optional(),
  favorite: z.enum(['true', 'false']).optional(),
  minScore: z.number().min(0).max(100).optional(),
  maxScore: z.number().min(0).max(100).optional(),
  tags: z.array(z.string()).optional()
})

// Student profile management
export const studentProfileUpdateSchema = z.object({
  name: nameSchema.optional(),
  bio: descriptionSchema,
  avatar: urlSchema.optional(),
  preferences: z.object({
    theme: z.enum(['light', 'dark', 'system']).optional(),
    language: z.enum(['en', 'hi']).optional(),
    notifications: z.object({
      email: booleanFlagSchema.optional(),
      push: booleanFlagSchema.optional(),
      quiz_reminders: booleanFlagSchema.optional(),
      achievement_alerts: booleanFlagSchema.optional(),
      leaderboard_updates: booleanFlagSchema.optional()
    }).optional(),
    privacy: z.object({
      showProfile: booleanFlagSchema.optional(),
      showStats: booleanFlagSchema.optional(),
      showAchievements: booleanFlagSchema.optional()
    }).optional()
  }).optional()
})

// Quiz attempt schemas
export const startQuizAttemptSchema = z.object({
  quizId: z.string().min(1, "Quiz ID is required")
})

export const submitAnswerSchema = z.object({
  questionId: z.string().min(1, "Question ID is required"),
  answer: z.union([
    z.string(), // For text answers and MCQ
    z.boolean(), // For true/false
    z.array(z.string()), // For matching pairs
    z.record(z.string()) // For complex answer structures
  ]),
  timeSpent: z.number().min(0).optional(), // Time spent on question in seconds
  confidence: z.number().min(1).max(5).optional() // Confidence level 1-5
})

export const submitQuizAttemptSchema = z.object({
  attemptId: z.string().min(1, "Attempt ID is required"),
  answers: z.array(submitAnswerSchema),
  totalTimeSpent: z.number().min(0).optional(), // Total time spent in seconds
  feedback: z.string().max(500).optional() // Optional feedback about the quiz
})

// Student history and analytics
export const studentHistoryQuerySchema = enhancedPaginationSchema.extend({
  quizType: quizTypeSchema.optional(),
  difficulty: difficultySchema.optional(),
  status: z.enum(['completed', 'in_progress', 'abandoned', 'all']).optional().default('all'),
  minScore: z.number().min(0).max(100).optional(),
  maxScore: z.number().min(0).max(100).optional(),
  dateFrom: dateTimeSchema.optional(),
  dateTo: dateTimeSchema.optional(),
  subject: z.string().optional()
})

export const studentAnalyticsQuerySchema = z.object({
  period: z.enum(['7d', '30d', '90d', '1y', 'all']).optional().default('30d'),
  subject: z.string().optional(),
  includeComparison: booleanFlagSchema.default(false),
  includeGoals: booleanFlagSchema.default(true)
})

// Practice session schemas
export const startPracticeSessionSchema = z.object({
  type: z.enum(['DAILY_PRACTICE', 'TOPIC_PRACTICE', 'WEAK_AREAS', 'RANDOM']),
  subject: z.string().optional(),
  chapter: z.string().optional(),
  topic: z.string().optional(),
  difficulty: difficultySchema.optional(),
  questionCount: z.number().min(5).max(50).default(10),
  timeLimit: z.number().min(5).max(60).optional(), // minutes
  questionTypes: z.array(questionTypeSchema).optional()
})

export const practiceSessionQuerySchema = enhancedPaginationSchema.extend({
  type: z.enum(['DAILY_PRACTICE', 'TOPIC_PRACTICE', 'WEAK_AREAS', 'RANDOM', 'all']).optional().default('all'),
  subject: z.string().optional(),
  dateFrom: dateTimeSchema.optional(),
  dateTo: dateTimeSchema.optional()
})

// Favorites management
export const manageFavoriteSchema = z.object({
  quizId: z.string().min(1, "Quiz ID is required"),
  action: z.enum(['add', 'remove'])
})

export const favoritesQuerySchema = enhancedPaginationSchema.extend({
  type: quizTypeSchema.optional(),
  difficulty: difficultySchema.optional(),
  subject: z.string().optional()
})

// Achievement and progress schemas
export const achievementQuerySchema = enhancedPaginationSchema.extend({
  category: z.enum(['quiz', 'streak', 'score', 'participation', 'special', 'all']).optional().default('all'),
  status: z.enum(['unlocked', 'locked', 'all']).optional().default('all')
})

export const goalSettingSchema = z.object({
  type: z.enum(['daily_quiz', 'weekly_score', 'monthly_streak', 'subject_mastery']),
  target: z.number().min(1),
  deadline: dateTimeSchema.optional(),
  subject: z.string().optional(),
  description: z.string().max(200).optional()
})

export const goalUpdateSchema = goalSettingSchema.partial()

// Leaderboard schemas
export const leaderboardQuerySchema = z.object({
  period: z.enum(['daily', 'weekly', 'monthly', 'all_time']).default('all_time'),
  category: z.enum(['points', 'quizzes', 'average_score', 'streak']).default('points'),
  subject: z.string().optional(),
  limit: z.number().min(1).max(100).default(10),
  includeMe: booleanFlagSchema.default(true)
})

// Study plan schemas
export const studyPlanQuerySchema = enhancedPaginationSchema.extend({
  status: z.enum(['active', 'completed', 'paused', 'all']).optional().default('all'),
  subject: z.string().optional()
})

export const createStudyPlanSchema = z.object({
  title: z.string().min(1).max(100),
  description: descriptionSchema,
  subject: z.string(),
  duration: z.number().min(1).max(365), // days
  dailyTarget: z.number().min(1).max(10), // quizzes per day
  difficulty: difficultySchema,
  topics: z.array(z.string()).min(1),
  startDate: dateTimeSchema.optional()
})

export const updateStudyPlanSchema = createStudyPlanSchema.partial()

// Discussion and community schemas
export const discussionQuerySchema = enhancedPaginationSchema.extend({
  quizId: z.string().optional(),
  subject: z.string().optional(),
  type: z.enum(['question', 'general', 'help', 'all']).optional().default('all'),
  status: z.enum(['open', 'answered', 'closed', 'all']).optional().default('all')
})

export const createDiscussionSchema = z.object({
  title: z.string().min(1).max(200),
  content: z.string().min(1).max(2000),
  type: z.enum(['question', 'general', 'help']),
  quizId: z.string().optional(),
  subject: z.string().optional(),
  tags: z.array(z.string()).max(5).optional()
})

export const replyDiscussionSchema = z.object({
  content: z.string().min(1).max(1000),
  parentId: z.string().optional() // For nested replies
})

// Notification preferences
export const notificationPreferencesSchema = z.object({
  email: z.object({
    quiz_reminders: booleanFlagSchema.default(true),
    achievement_alerts: booleanFlagSchema.default(true),
    leaderboard_updates: booleanFlagSchema.default(false),
    study_plan_reminders: booleanFlagSchema.default(true),
    discussion_replies: booleanFlagSchema.default(true)
  }),
  push: z.object({
    quiz_reminders: booleanFlagSchema.default(true),
    achievement_alerts: booleanFlagSchema.default(true),
    leaderboard_updates: booleanFlagSchema.default(false),
    study_plan_reminders: booleanFlagSchema.default(true),
    discussion_replies: booleanFlagSchema.default(false)
  }),
  frequency: z.object({
    daily_summary: booleanFlagSchema.default(true),
    weekly_report: booleanFlagSchema.default(true),
    monthly_report: booleanFlagSchema.default(false)
  })
})

// Dashboard customization
export const dashboardCustomizationSchema = z.object({
  widgets: z.array(z.object({
    type: z.enum(['recent_quizzes', 'achievements', 'leaderboard', 'study_plan', 'analytics', 'goals']),
    position: z.number().min(0),
    size: z.enum(['small', 'medium', 'large']).default('medium'),
    visible: booleanFlagSchema.default(true)
  })),
  theme: z.enum(['light', 'dark', 'auto']).default('auto'),
  compactMode: booleanFlagSchema.default(false)
})

// Certificate request schema
export const certificateRequestSchema = z.object({
  quizId: z.string().min(1, "Quiz ID is required"),
  attemptId: z.string().min(1, "Attempt ID is required"),
  template: z.enum(['modern', 'classic', 'minimal']).default('modern'),
  includeScore: booleanFlagSchema.default(true),
  includeDate: booleanFlagSchema.default(true)
})

// Export all student schemas
export const studentSchemas = {
  // Quiz browsing and attempts
  studentQuizQuery: studentQuizQuerySchema,
  startQuizAttempt: startQuizAttemptSchema,
  submitAnswer: submitAnswerSchema,
  submitQuizAttempt: submitQuizAttemptSchema,
  
  // Profile and preferences
  studentProfileUpdate: studentProfileUpdateSchema,
  notificationPreferences: notificationPreferencesSchema,
  dashboardCustomization: dashboardCustomizationSchema,
  
  // History and analytics
  studentHistoryQuery: studentHistoryQuerySchema,
  studentAnalyticsQuery: studentAnalyticsQuerySchema,
  
  // Practice sessions
  startPracticeSession: startPracticeSessionSchema,
  practiceSessionQuery: practiceSessionQuerySchema,
  
  // Favorites
  manageFavorite: manageFavoriteSchema,
  favoritesQuery: favoritesQuerySchema,
  
  // Achievements and goals
  achievementQuery: achievementQuerySchema,
  goalSetting: goalSettingSchema,
  goalUpdate: goalUpdateSchema,
  
  // Leaderboard
  leaderboardQuery: leaderboardQuerySchema,
  
  // Study plans
  studyPlanQuery: studyPlanQuerySchema,
  createStudyPlan: createStudyPlanSchema,
  updateStudyPlan: updateStudyPlanSchema,
  
  // Discussions
  discussionQuery: discussionQuerySchema,
  createDiscussion: createDiscussionSchema,
  replyDiscussion: replyDiscussionSchema,
  
  // Certificates
  certificateRequest: certificateRequestSchema
}
