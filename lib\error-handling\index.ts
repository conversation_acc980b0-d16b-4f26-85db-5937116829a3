import { toast } from 'sonner'

// ============================================================================
// UNIFIED ERROR HANDLING SYSTEM
// ============================================================================

// Error types
export enum ErrorType {
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  NETWORK = 'NETWORK',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN'
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL'
}

// Base error interface
export interface AppError {
  type: ErrorType
  severity: ErrorSeverity
  message: string
  code?: string
  details?: any
  timestamp: Date
  userId?: string
  context?: Record<string, any>
  stack?: string
}

// Error handler configuration
interface ErrorHandlerConfig {
  showToast?: boolean
  logToConsole?: boolean
  logToServer?: boolean
  retryable?: boolean
  retryCount?: number
  retryDelay?: number
  fallbackMessage?: string
  onError?: (error: AppError) => void
  onRetry?: (attempt: number) => void
}

// Default error handler configuration
const defaultConfig: ErrorHandlerConfig = {
  showToast: true,
  logToConsole: true,
  logToServer: false,
  retryable: false,
  retryCount: 3,
  retryDelay: 1000,
  fallbackMessage: 'An unexpected error occurred'
}

// Error classification utility
export function classifyError(error: any): { type: ErrorType; severity: ErrorSeverity } {
  // Network errors
  if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
    return { type: ErrorType.NETWORK, severity: ErrorSeverity.MEDIUM }
  }
  
  // HTTP status code based classification
  if (error.status || error.response?.status) {
    const status = error.status || error.response.status
    
    switch (Math.floor(status / 100)) {
      case 4:
        if (status === 401) {
          return { type: ErrorType.AUTHENTICATION, severity: ErrorSeverity.HIGH }
        }
        if (status === 403) {
          return { type: ErrorType.AUTHORIZATION, severity: ErrorSeverity.HIGH }
        }
        if (status === 404) {
          return { type: ErrorType.NOT_FOUND, severity: ErrorSeverity.LOW }
        }
        if (status === 422) {
          return { type: ErrorType.VALIDATION, severity: ErrorSeverity.LOW }
        }
        return { type: ErrorType.CLIENT, severity: ErrorSeverity.MEDIUM }
      
      case 5:
        return { type: ErrorType.SERVER, severity: ErrorSeverity.HIGH }
      
      default:
        return { type: ErrorType.UNKNOWN, severity: ErrorSeverity.MEDIUM }
    }
  }
  
  // Validation errors
  if (error.name === 'ValidationError' || error.code === 'VALIDATION_ERROR') {
    return { type: ErrorType.VALIDATION, severity: ErrorSeverity.LOW }
  }
  
  // Default classification
  return { type: ErrorType.UNKNOWN, severity: ErrorSeverity.MEDIUM }
}

// Create standardized error object
export function createAppError(
  error: any,
  context?: Record<string, any>,
  userId?: string
): AppError {
  const { type, severity } = classifyError(error)
  
  return {
    type,
    severity,
    message: error.message || error.toString() || 'Unknown error',
    code: error.code || error.name,
    details: error.details || error.response?.data,
    timestamp: new Date(),
    userId,
    context,
    stack: error.stack
  }
}

// Error logging service
class ErrorLogger {
  static async logToServer(error: AppError): Promise<void> {
    try {
      await fetch('/api/errors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: error.type,
          severity: error.severity,
          message: error.message,
          code: error.code,
          details: error.details,
          timestamp: error.timestamp.toISOString(),
          userId: error.userId,
          context: error.context,
          stack: error.stack,
          userAgent: navigator.userAgent,
          url: window.location.href
        })
      })
    } catch (logError) {
      console.error('Failed to log error to server:', logError)
    }
  }
  
  static logToConsole(error: AppError): void {
    const logMethod = error.severity === ErrorSeverity.CRITICAL ? 'error' : 
                     error.severity === ErrorSeverity.HIGH ? 'error' :
                     error.severity === ErrorSeverity.MEDIUM ? 'warn' : 'log'
    
    console[logMethod]('App Error:', {
      type: error.type,
      severity: error.severity,
      message: error.message,
      code: error.code,
      details: error.details,
      context: error.context,
      timestamp: error.timestamp
    })
    
    if (error.stack) {
      console[logMethod]('Stack trace:', error.stack)
    }
  }
}

// Toast notification service
class ErrorToastService {
  static show(error: AppError, config: ErrorHandlerConfig): void {
    const message = this.getDisplayMessage(error, config)
    
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
      case ErrorSeverity.HIGH:
        toast.error(message, {
          duration: 10000,
          action: config.retryable ? {
            label: 'Retry',
            onClick: () => config.onRetry?.(1)
          } : undefined
        })
        break
      
      case ErrorSeverity.MEDIUM:
        toast.error(message, { duration: 5000 })
        break
      
      case ErrorSeverity.LOW:
        toast.warning(message, { duration: 3000 })
        break
    }
  }
  
  private static getDisplayMessage(error: AppError, config: ErrorHandlerConfig): string {
    // Use fallback message for server errors to avoid exposing internal details
    if (error.type === ErrorType.SERVER && config.fallbackMessage) {
      return config.fallbackMessage
    }
    
    // Use specific messages for known error types
    switch (error.type) {
      case ErrorType.AUTHENTICATION:
        return 'Please log in to continue'
      case ErrorType.AUTHORIZATION:
        return 'You do not have permission to perform this action'
      case ErrorType.NOT_FOUND:
        return 'The requested resource was not found'
      case ErrorType.NETWORK:
        return 'Network error. Please check your connection and try again'
      case ErrorType.VALIDATION:
        return error.message || 'Please check your input and try again'
      default:
        return error.message || config.fallbackMessage || 'An error occurred'
    }
  }
}

// Retry mechanism
class RetryService {
  static async withRetry<T>(
    operation: () => Promise<T>,
    config: ErrorHandlerConfig
  ): Promise<T> {
    let lastError: any
    const maxAttempts = config.retryCount || 3
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        // Don't retry on certain error types
        const { type } = classifyError(error)
        if ([ErrorType.AUTHENTICATION, ErrorType.AUTHORIZATION, ErrorType.VALIDATION].includes(type)) {
          throw error
        }
        
        // Don't retry on last attempt
        if (attempt === maxAttempts) {
          throw error
        }
        
        // Call retry callback
        config.onRetry?.(attempt)
        
        // Wait before retrying
        if (config.retryDelay) {
          await new Promise(resolve => setTimeout(resolve, config.retryDelay * attempt))
        }
      }
    }
    
    throw lastError
  }
}

// Main error handler
export class ErrorHandler {
  static async handle(
    error: any,
    context?: Record<string, any>,
    config: Partial<ErrorHandlerConfig> = {}
  ): Promise<AppError> {
    const finalConfig = { ...defaultConfig, ...config }
    const appError = createAppError(error, context)
    
    // Log to console
    if (finalConfig.logToConsole) {
      ErrorLogger.logToConsole(appError)
    }
    
    // Log to server
    if (finalConfig.logToServer) {
      await ErrorLogger.logToServer(appError)
    }
    
    // Show toast notification
    if (finalConfig.showToast) {
      ErrorToastService.show(appError, finalConfig)
    }
    
    // Call custom error handler
    if (finalConfig.onError) {
      finalConfig.onError(appError)
    }
    
    return appError
  }
  
  static async handleWithRetry<T>(
    operation: () => Promise<T>,
    context?: Record<string, any>,
    config: Partial<ErrorHandlerConfig> = {}
  ): Promise<T> {
    const finalConfig = { ...defaultConfig, retryable: true, ...config }
    
    try {
      if (finalConfig.retryable) {
        return await RetryService.withRetry(operation, finalConfig)
      } else {
        return await operation()
      }
    } catch (error) {
      await this.handle(error, context, finalConfig)
      throw error
    }
  }
}

// Utility functions for common error handling patterns
export const ErrorUtils = {
  // Handle API fetch errors
  async handleFetchError(response: Response, context?: Record<string, any>): Promise<never> {
    let errorData: any = {}
    
    try {
      errorData = await response.json()
    } catch {
      // Response might not be JSON
    }
    
    const error = {
      status: response.status,
      statusText: response.statusText,
      message: errorData.message || errorData.error || `HTTP ${response.status}: ${response.statusText}`,
      details: errorData,
      code: errorData.code
    }
    
    await ErrorHandler.handle(error, context)
    throw error
  },
  
  // Handle form validation errors
  handleValidationErrors(errors: Record<string, string[]>): void {
    const errorMessages = Object.entries(errors)
      .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
      .join('\n')
    
    ErrorHandler.handle({
      type: ErrorType.VALIDATION,
      message: errorMessages,
      details: errors
    }, {}, { showToast: true })
  },
  
  // Handle async operation errors
  async safeAsync<T>(
    operation: () => Promise<T>,
    fallback?: T,
    context?: Record<string, any>
  ): Promise<T | undefined> {
    try {
      return await operation()
    } catch (error) {
      await ErrorHandler.handle(error, context, { showToast: false })
      return fallback
    }
  },
  
  // Create error boundary handler
  createErrorBoundaryHandler(componentName: string) {
    return (error: Error, errorInfo: any) => {
      ErrorHandler.handle(error, {
        component: componentName,
        errorInfo
      }, {
        severity: ErrorSeverity.HIGH,
        logToServer: true
      })
    }
  }
}

// Global error handler setup
export function setupGlobalErrorHandling(): void {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    ErrorHandler.handle(event.reason, {
      type: 'unhandledrejection',
      promise: event.promise
    }, {
      severity: ErrorSeverity.HIGH,
      logToServer: true
    })
  })

  // Handle uncaught errors
  window.addEventListener('error', (event) => {
    ErrorHandler.handle(event.error || new Error(event.message), {
      type: 'uncaughtError',
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    }, {
      severity: ErrorSeverity.HIGH,
      logToServer: true
    })
  })

  // Handle resource loading errors
  window.addEventListener('error', (event) => {
    if (event.target !== window) {
      ErrorHandler.handle(new Error(`Failed to load resource: ${(event.target as any)?.src || 'unknown'}`), {
        type: 'resourceError',
        target: event.target
      }, {
        severity: ErrorSeverity.LOW,
        showToast: false,
        logToServer: true
      })
    }
  }, true)
}
