"use client"

import { ReactNode } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { MoreHorizontal, ExternalLink } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface BaseCardProps {
  title?: string
  description?: string
  children: ReactNode
  className?: string
  headerContent?: ReactNode
  footerContent?: ReactNode
  actions?: Array<{
    label: string
    onClick: () => void
    variant?: "default" | "destructive" | "outline" | "secondary" | "ghost"
  }>
}

export function BaseCard({
  title,
  description,
  children,
  className,
  headerContent,
  footerContent,
  actions
}: BaseCardProps) {
  return (
    <Card className={className}>
      {(title || description || headerContent) && (
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              {title && <CardTitle>{title}</CardTitle>}
              {description && <CardDescription>{description}</CardDescription>}
            </div>
            {headerContent}
          </div>
        </CardHeader>
      )}
      <CardContent>
        {children}
      </CardContent>
      {(footerContent || actions) && (
        <>
          <Separator />
          <div className="p-6 pt-4">
            {footerContent}
            {actions && (
              <div className="flex gap-2 mt-4">
                {actions.map((action, index) => (
                  <Button
                    key={index}
                    variant={action.variant || "default"}
                    onClick={action.onClick}
                    size="sm"
                  >
                    {action.label}
                  </Button>
                ))}
              </div>
            )}
          </div>
        </>
      )}
    </Card>
  )
}

interface StatsCardProps {
  title: string
  value: string | number
  description?: string
  trend?: {
    value: number
    label: string
    positive?: boolean
  }
  icon?: ReactNode
  className?: string
}

export function StatsCard({
  title,
  value,
  description,
  trend,
  icon,
  className
}: StatsCardProps) {
  return (
    <Card className={className}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {description && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
          </div>
          {icon && (
            <div className="h-8 w-8 text-muted-foreground">
              {icon}
            </div>
          )}
        </div>
        {trend && (
          <div className="mt-4 flex items-center gap-2">
            <span className={cn(
              "text-xs font-medium",
              trend.positive ? "text-green-600" : "text-red-600"
            )}>
              {trend.positive ? "+" : ""}{trend.value}%
            </span>
            <span className="text-xs text-muted-foreground">{trend.label}</span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

interface ActionCardProps {
  title: string
  description?: string
  icon?: ReactNode
  href?: string
  onClick?: () => void
  badge?: string
  disabled?: boolean
  className?: string
}

export function ActionCard({
  title,
  description,
  icon,
  href,
  onClick,
  badge,
  disabled = false,
  className
}: ActionCardProps) {
  const CardComponent = href ? "a" : "button"
  
  return (
    <Card className={cn(
      "transition-colors hover:bg-accent cursor-pointer",
      disabled && "opacity-50 cursor-not-allowed",
      className
    )}>
      <CardComponent
        href={href}
        onClick={!disabled ? onClick : undefined}
        className="w-full text-left p-0 bg-transparent border-none"
        disabled={disabled}
      >
        <CardContent className="p-6">
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-4">
              {icon && (
                <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
                  {icon}
                </div>
              )}
              <div className="space-y-1">
                <h3 className="font-semibold">{title}</h3>
                {description && (
                  <p className="text-sm text-muted-foreground">{description}</p>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              {badge && <Badge variant="secondary">{badge}</Badge>}
              {href && <ExternalLink className="h-4 w-4 text-muted-foreground" />}
            </div>
          </div>
        </CardContent>
      </CardComponent>
    </Card>
  )
}

interface ListCardProps {
  title?: string
  description?: string
  items: Array<{
    id: string
    title: string
    description?: string
    badge?: string
    actions?: Array<{
      label: string
      onClick: () => void
    }>
  }>
  className?: string
  emptyMessage?: string
}

export function ListCard({
  title,
  description,
  items,
  className,
  emptyMessage = "No items found"
}: ListCardProps) {
  return (
    <Card className={className}>
      {(title || description) && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <CardDescription>{description}</CardDescription>}
        </CardHeader>
      )}
      <CardContent className="p-0">
        {items.length === 0 ? (
          <div className="p-6 text-center text-muted-foreground">
            {emptyMessage}
          </div>
        ) : (
          <div className="divide-y">
            {items.map((item, index) => (
              <div key={item.id} className="p-4 flex items-center justify-between">
                <div className="space-y-1">
                  <h4 className="font-medium">{item.title}</h4>
                  {item.description && (
                    <p className="text-sm text-muted-foreground">
                      {item.description}
                    </p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  {item.badge && (
                    <Badge variant="secondary">{item.badge}</Badge>
                  )}
                  {item.actions && item.actions.length > 0 && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {item.actions.map((action, actionIndex) => (
                          <DropdownMenuItem
                            key={actionIndex}
                            onClick={action.onClick}
                          >
                            {action.label}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

interface FeatureCardProps {
  title: string
  description: string
  icon?: ReactNode
  features: string[]
  primaryAction?: {
    label: string
    onClick: () => void
  }
  secondaryAction?: {
    label: string
    onClick: () => void
  }
  className?: string
}

export function FeatureCard({
  title,
  description,
  icon,
  features,
  primaryAction,
  secondaryAction,
  className
}: FeatureCardProps) {
  return (
    <Card className={className}>
      <CardHeader className="text-center">
        {icon && (
          <div className="mx-auto h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center mb-4">
            {icon}
          </div>
        )}
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <ul className="space-y-2">
          {features.map((feature, index) => (
            <li key={index} className="flex items-center gap-2 text-sm">
              <div className="h-1.5 w-1.5 rounded-full bg-primary" />
              {feature}
            </li>
          ))}
        </ul>
        
        {(primaryAction || secondaryAction) && (
          <div className="flex flex-col gap-2 pt-4">
            {primaryAction && (
              <Button onClick={primaryAction.onClick} className="w-full">
                {primaryAction.label}
              </Button>
            )}
            {secondaryAction && (
              <Button
                variant="outline"
                onClick={secondaryAction.onClick}
                className="w-full"
              >
                {secondaryAction.label}
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

interface GridLayoutProps {
  children: ReactNode
  columns?: 1 | 2 | 3 | 4
  gap?: "sm" | "md" | "lg"
  className?: string
}

export function GridLayout({
  children,
  columns = 3,
  gap = "md",
  className
}: GridLayoutProps) {
  const columnClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 md:grid-cols-2",
    3: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
  }

  const gapClasses = {
    sm: "gap-4",
    md: "gap-6",
    lg: "gap-8"
  }

  return (
    <div className={cn(
      "grid",
      columnClasses[columns],
      gapClasses[gap],
      className
    )}>
      {children}
    </div>
  )
}
