import { z } from "zod"

// ============================================================================
// COMMON VALIDATION SCHEMAS
// ============================================================================

// Base pagination schema used across all paginated endpoints
export const paginationSchema = z.object({
  page: z.string().optional().transform(val => parseInt(val || '1') || 1),
  limit: z.string().optional().transform(val => Math.min(parseInt(val || '20') || 20, 100)),
  sort: z.string().optional(),
  order: z.enum(['asc', 'desc']).optional().default('desc')
})

// Enhanced pagination with common filters
export const enhancedPaginationSchema = paginationSchema.extend({
  search: z.string().optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc')
})

// Common search and filter schema
export const searchSchema = z.object({
  q: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  status: z.string().optional()
})

// ID parameter validation
export const idParamSchema = z.object({
  id: z.string().min(1, 'ID is required')
})

// Common enum schemas
export const userRoleSchema = z.enum(['STUDENT', 'ADMIN'])
export const quizTypeSchema = z.enum(['QUIZ', 'TEST_SERIES', 'DAILY_PRACTICE'])
export const difficultySchema = z.enum(['EASY', 'MEDIUM', 'HARD'])
export const questionTypeSchema = z.enum(['MCQ', 'TRUE_FALSE', 'SHORT_ANSWER', 'MATCHING'])

// Date and time validation
export const dateTimeSchema = z.string().datetime("Invalid date format")
export const dateRangeSchema = z.object({
  startDate: dateTimeSchema,
  endDate: dateTimeSchema
}).refine(data => new Date(data.startDate) < new Date(data.endDate), {
  message: "End date must be after start date"
})

// File validation schemas
export const fileTypeSchema = z.enum(['pdf', 'doc', 'docx', 'txt', 'jpg', 'png', 'jpeg'])
export const fileSizeSchema = z.number().min(1).max(100 * 1024 * 1024) // 100MB max

// Common field validations
export const emailSchema = z.string().email("Invalid email format")
export const nameSchema = z.string().min(1, "Name is required").max(100, "Name too long")
export const descriptionSchema = z.string().max(1000, "Description too long").optional()
export const urlSchema = z.string().url("Invalid URL format")

// Tag validation
export const tagSchema = z.string().min(1).max(50)
export const tagsArraySchema = z.array(tagSchema).max(20, "Too many tags")

// Score and percentage validation
export const scoreSchema = z.number().min(0).max(100)
export const percentageSchema = z.number().min(0).max(100)

// Time validation (in minutes)
export const timeLimitSchema = z.number().min(1, "Time limit must be at least 1 minute")
export const durationSchema = z.number().min(1, "Duration must be at least 1 minute")

// Attempt validation
export const maxAttemptsSchema = z.number().min(1, "Must allow at least 1 attempt").max(10, "Too many attempts")

// Common boolean flags
export const booleanFlagSchema = z.boolean().default(false)
export const publishedSchema = z.boolean().default(false)

// Language schema
export const languageSchema = z.enum(['ENGLISH', 'HINDI', 'BILINGUAL'])

// Theme schema
export const themeSchema = z.enum(['light', 'dark', 'system'])

// Status schemas
export const quizStatusSchema = z.enum(['DRAFT', 'PUBLISHED', 'ARCHIVED'])
export const attemptStatusSchema = z.enum(['IN_PROGRESS', 'COMPLETED', 'ABANDONED', 'EXPIRED'])

// Common response schemas
export const successResponseSchema = z.object({
  success: z.literal(true),
  data: z.any(),
  message: z.string().optional(),
  timestamp: z.string().datetime()
})

export const errorResponseSchema = z.object({
  success: z.literal(false),
  error: z.string(),
  code: z.string().optional(),
  details: z.any().optional(),
  timestamp: z.string().datetime()
})

// Utility functions for schema composition
export const createPaginatedResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) => z.object({
  success: z.literal(true),
  data: z.array(dataSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean()
  }),
  timestamp: z.string().datetime()
})

// Create filtered query schema with common filters
export const createFilteredQuerySchema = (additionalFilters?: z.ZodRawShape) => {
  const baseSchema = enhancedPaginationSchema.extend({
    type: quizTypeSchema.optional(),
    difficulty: difficultySchema.optional(),
    status: z.string().optional()
  })
  
  if (additionalFilters) {
    return baseSchema.extend(additionalFilters)
  }
  
  return baseSchema
}

// Create CRUD operation schemas
export const createCRUDSchemas = <T extends z.ZodRawShape>(fields: T) => ({
  create: z.object(fields),
  update: z.object(
    Object.fromEntries(
      Object.entries(fields).map(([key, schema]) => [key, (schema as any).optional()])
    ) as { [K in keyof T]: z.ZodOptional<T[K]> }
  ),
  query: createFilteredQuerySchema()
})

// Export commonly used composed schemas
export const commonQuerySchemas = {
  pagination: paginationSchema,
  enhancedPagination: enhancedPaginationSchema,
  search: searchSchema,
  filtered: createFilteredQuerySchema()
}

export const commonFieldSchemas = {
  id: idParamSchema,
  email: emailSchema,
  name: nameSchema,
  description: descriptionSchema,
  url: urlSchema,
  tags: tagsArraySchema,
  score: scoreSchema,
  timeLimit: timeLimitSchema,
  maxAttempts: maxAttemptsSchema
}

export const commonEnumSchemas = {
  userRole: userRoleSchema,
  quizType: quizTypeSchema,
  difficulty: difficultySchema,
  questionType: questionTypeSchema,
  language: languageSchema,
  theme: themeSchema,
  quizStatus: quizStatusSchema,
  attemptStatus: attemptStatusSchema
}
