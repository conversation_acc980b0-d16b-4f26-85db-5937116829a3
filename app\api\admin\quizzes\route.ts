import { NextRequest } from 'next/server'
import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, APIResponse } from '@/lib/api-middleware'
import { createQuizSchema, quizQuerySchema } from '@/lib/schemas/quiz'
import { prisma } from '@/lib/prisma'

// POST /api/admin/quizzes - Create a new quiz
export const POST = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateBody: createQuizSchema
  },
  async (request: NextRequest, { user, validatedBody }) => {
    try {
      // Create quiz with questions in a transaction
      const quiz = await prisma.$transaction(async (tx) => {
        // Create the quiz
        const newQuiz = await tx.quiz.create({
          data: {
            title: validatedBody.title,
            description: validatedBody.description,
            type: validatedBody.type,
            difficulty: validatedBody.difficulty,
            tags: validatedBody.tags,
            timeLimit: validatedBody.timeLimit,
            startTime: validatedBody.startTime ? new Date(validatedBody.startTime) : null,
            endTime: validatedBody.endTime ? new Date(validatedBody.endTime) : null,
            maxAttempts: validatedBody.maxAttempts,
            passingScore: validatedBody.passingScore,
            instructions: validatedBody.instructions,
            thumbnail: validatedBody.thumbnail,
            isPublished: validatedBody.isPublished,
            subjectId: validatedBody.subjectId,
            chapterId: validatedBody.chapterId,
            topicId: validatedBody.topicId,
            createdBy: user.id,
          }
        })

        // Create questions
        if (validatedBody.questions && validatedBody.questions.length > 0) {
          await tx.question.createMany({
            data: validatedBody.questions.map((question, index) => ({
              quizId: newQuiz.id,
              type: question.type,
              text: question.text,
              options: question.options,
              correctAnswer: question.correctAnswer,
              explanation: question.explanation,
              points: question.points,
              image: question.image,
              order: question.order || index + 1,
            }))
          })
        }

        return newQuiz
      })

      return APIResponse.created(quiz, 'Quiz created successfully')

    } catch (error) {
      console.error('Error creating quiz:', error)
      return APIResponse.internalServerError('Failed to create quiz')
    }
  }
)

// GET /api/admin/quizzes - Get all quizzes for admin
export const GET = createAPIHandler(
  {
    requireAuth: true,
    requireRole: 'ADMIN',
    validateQuery: quizQuerySchema
  },
  async (request: NextRequest, { user, validatedQuery }) => {
    try {
      const {
        page,
        limit,
        search,
        type,
        status,
        creatorId
      } = validatedQuery

      // Build where clause
      const where: any = {}

      if (search) {
        where.OR = [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } }
        ]
      }

      if (type) {
        where.type = type
      }

      if (status === 'published') {
        where.isPublished = true
      } else if (status === 'draft') {
        where.isPublished = false
      }

      if (creatorId) {
        where.creatorId = creatorId
      }

      const skip = (page - 1) * limit

      const [quizzes, total] = await Promise.all([
        prisma.quiz.findMany({
          where,
          include: {
            creator: {
              select: { name: true, email: true }
            },
            _count: {
              select: {
                questions: true,
                attempts: true
              }
            },
            attempts: {
              select: { score: true }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.quiz.count({ where })
      ])

      // Calculate statistics for each quiz
      const quizzesWithStats = quizzes.map(quiz => ({
        id: quiz.id,
        title: quiz.title,
        description: quiz.description,
        type: quiz.type,
        difficulty: quiz.difficulty,
        tags: quiz.tags,
        isPublished: quiz.isPublished,
        createdAt: quiz.createdAt.toISOString(),
        creator: quiz.creator,
        questionCount: quiz._count.questions,
        attemptCount: quiz._count.attempts,
        averageScore: quiz.attempts.length > 0
          ? Math.round(quiz.attempts.reduce((sum, attempt) => sum + attempt.score, 0) / quiz.attempts.length)
          : 0
      }))

      const totalPages = Math.ceil(total / limit)
      const hasNext = page < totalPages
      const hasPrev = page > 1

      return APIResponse.paginated(
        quizzesWithStats,
        { page, limit, total, totalPages, hasNext, hasPrev },
        'Quizzes retrieved successfully'
      )

    } catch (error) {
      console.error('Error fetching admin quizzes:', error)
      return APIResponse.internalServerError('Failed to fetch quizzes')
    }
  }
)
