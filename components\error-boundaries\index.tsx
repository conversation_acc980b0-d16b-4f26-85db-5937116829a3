"use client"

import React, { <PERSON>mpo<PERSON>, ReactNode } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { 
  AlertTriangle, 
  RefreshCw, 
  Home, 
  Bug, 
  ChevronDown, 
  ChevronUp,
  Copy,
  ExternalLink
} from "lucide-react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorSeverity } from "@/lib/error-handling"
import { cn } from "@/lib/utils"
import { toast } from 'sonner'

// ============================================================================
// ERROR BOUNDARY COMPONENTS
// ============================================================================

interface ErrorBoundaryState {
  hasError: boolean
  error: Error | null
  errorInfo: React.ErrorInfo | null
  showDetails: boolean
}

interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void
  showReload?: boolean
  showHome?: boolean
  showDetails?: boolean
  level?: 'page' | 'section' | 'component'
  name?: string
}

// Base Error Boundary Component
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({ errorInfo })
    
    // Handle error with our error handling system
    const handler = ErrorUtils.createErrorBoundaryHandler(this.props.name || 'ErrorBoundary')
    handler(error, errorInfo)
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo)
    }
  }

  handleReload = () => {
    window.location.reload()
  }

  handleGoHome = () => {
    window.location.href = '/'
  }

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false
    })
  }

  handleCopyError = () => {
    const errorText = `Error: ${this.state.error?.message}\n\nStack: ${this.state.error?.stack}\n\nComponent Stack: ${this.state.errorInfo?.componentStack}`
    navigator.clipboard.writeText(errorText).then(() => {
      toast.success('Error details copied to clipboard')
    }).catch(() => {
      toast.error('Failed to copy error details')
    })
  }

  handleReportError = () => {
    // Open error reporting form or external service
    const subject = encodeURIComponent(`Error Report: ${this.state.error?.message}`)
    const body = encodeURIComponent(`Error Details:\n${this.state.error?.stack}\n\nComponent: ${this.props.name}\nURL: ${window.location.href}`)
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`)
  }

  toggleDetails = () => {
    this.setState(prev => ({ showDetails: !prev.showDetails }))
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback
      }

      // Render appropriate error UI based on level
      return this.renderErrorUI()
    }

    return this.props.children
  }

  private renderErrorUI() {
    const { level = 'component', showReload = true, showHome = true, showDetails = true } = this.props
    const { error, errorInfo, showDetails: detailsVisible } = this.state

    switch (level) {
      case 'page':
        return this.renderPageError()
      case 'section':
        return this.renderSectionError()
      default:
        return this.renderComponentError()
    }
  }

  private renderPageError() {
    const { error, errorInfo, showDetails } = this.state

    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <Card className="w-full max-w-2xl">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="h-8 w-8 text-destructive" />
            </div>
            <CardTitle className="text-2xl">Something went wrong</CardTitle>
            <CardDescription>
              We encountered an unexpected error. Our team has been notified and is working on a fix.
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="flex justify-center gap-3">
              <Button onClick={this.handleRetry} variant="default">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
              <Button onClick={this.handleGoHome} variant="outline">
                <Home className="h-4 w-4 mr-2" />
                Go Home
              </Button>
              <Button onClick={this.handleReload} variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Reload Page
              </Button>
            </div>

            {this.props.showDetails && (
              <>
                <Separator />
                <div className="space-y-3">
                  <Button
                    variant="ghost"
                    onClick={this.toggleDetails}
                    className="w-full justify-between"
                  >
                    <span className="flex items-center gap-2">
                      <Bug className="h-4 w-4" />
                      Error Details
                    </span>
                    {showDetails ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  </Button>
                  
                  {showDetails && (
                    <div className="space-y-3">
                      <div className="p-3 bg-muted rounded-lg">
                        <p className="text-sm font-medium text-destructive mb-2">Error Message:</p>
                        <p className="text-sm font-mono">{error?.message}</p>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline" onClick={this.handleCopyError}>
                          <Copy className="h-4 w-4 mr-2" />
                          Copy Details
                        </Button>
                        <Button size="sm" variant="outline" onClick={this.handleReportError}>
                          <ExternalLink className="h-4 w-4 mr-2" />
                          Report Issue
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  private renderSectionError() {
    return (
      <Card className="border-destructive/20">
        <CardContent className="p-6">
          <div className="flex items-start gap-4">
            <div className="p-2 bg-destructive/10 rounded-full">
              <AlertTriangle className="h-5 w-5 text-destructive" />
            </div>
            <div className="flex-1 space-y-3">
              <div>
                <h3 className="font-semibold text-destructive">Section Error</h3>
                <p className="text-sm text-muted-foreground">
                  This section encountered an error and couldn't load properly.
                </p>
              </div>
              <div className="flex gap-2">
                <Button size="sm" onClick={this.handleRetry}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
                {this.props.showDetails && (
                  <Button size="sm" variant="outline" onClick={this.toggleDetails}>
                    <Bug className="h-4 w-4 mr-2" />
                    Details
                  </Button>
                )}
              </div>
              
              {this.state.showDetails && (
                <div className="mt-3 p-3 bg-muted rounded text-xs font-mono">
                  {this.state.error?.message}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  private renderComponentError() {
    return (
      <div className="p-4 border border-destructive/20 rounded-lg bg-destructive/5">
        <div className="flex items-center gap-3">
          <AlertTriangle className="h-4 w-4 text-destructive flex-shrink-0" />
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-destructive">Component Error</p>
            <p className="text-xs text-muted-foreground truncate">
              {this.state.error?.message || 'An error occurred in this component'}
            </p>
          </div>
          <Button size="sm" variant="ghost" onClick={this.handleRetry}>
            <RefreshCw className="h-3 w-3" />
          </Button>
        </div>
      </div>
    )
  }
}

// Specialized Error Boundaries

// Page-level error boundary
export function PageErrorBoundary({ children, name }: { children: ReactNode; name?: string }) {
  return (
    <ErrorBoundary
      level="page"
      name={name || 'Page'}
      showReload={true}
      showHome={true}
      showDetails={true}
    >
      {children}
    </ErrorBoundary>
  )
}

// Section-level error boundary
export function SectionErrorBoundary({ children, name }: { children: ReactNode; name?: string }) {
  return (
    <ErrorBoundary
      level="section"
      name={name || 'Section'}
      showReload={false}
      showHome={false}
      showDetails={true}
    >
      {children}
    </ErrorBoundary>
  )
}

// Component-level error boundary
export function ComponentErrorBoundary({ children, name }: { children: ReactNode; name?: string }) {
  return (
    <ErrorBoundary
      level="component"
      name={name || 'Component'}
      showReload={false}
      showHome={false}
      showDetails={false}
    >
      {children}
    </ErrorBoundary>
  )
}

// HOC for wrapping components with error boundaries
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Partial<ErrorBoundaryProps>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )
  
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Hook for handling errors in functional components
export function useErrorHandler() {
  return {
    handleError: (error: Error, context?: Record<string, any>) => {
      ErrorHandler.handle(error, context, {
        severity: ErrorSeverity.MEDIUM,
        showToast: true,
        logToServer: true
      })
    },
    
    handleAsyncError: async (operation: () => Promise<any>, context?: Record<string, any>) => {
      try {
        return await operation()
      } catch (error) {
        ErrorHandler.handle(error as Error, context, {
          severity: ErrorSeverity.MEDIUM,
          showToast: true,
          logToServer: true
        })
        throw error
      }
    }
  }
}
