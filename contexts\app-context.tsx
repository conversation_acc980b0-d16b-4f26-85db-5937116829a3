"use client"

import { createContext, useContext, useReducer, useEffect, ReactNode } from 'react'
import { useSession } from 'next-auth/react'
import { toast } from 'sonner'

// ============================================================================
// SHARED APPLICATION STATE MANAGEMENT
// ============================================================================

// Application state interface
interface AppState {
  user: {
    data: any | null
    loading: boolean
    error: string | null
  }
  notifications: {
    items: any[]
    unreadCount: number
    loading: boolean
  }
  settings: {
    theme: 'light' | 'dark' | 'system'
    language: 'en' | 'hi'
    notifications: {
      email: boolean
      push: boolean
      sound: boolean
    }
    privacy: {
      showProfile: boolean
      showStats: boolean
      showAchievements: boolean
    }
  }
  ui: {
    sidebarOpen: boolean
    loading: boolean
    error: string | null
  }
  realtime: {
    connected: boolean
    activeUsers: number
    systemStatus: 'online' | 'offline' | 'maintenance'
  }
}

// Action types
type AppAction =
  | { type: 'SET_USER'; payload: any }
  | { type: 'SET_USER_LOADING'; payload: boolean }
  | { type: 'SET_USER_ERROR'; payload: string | null }
  | { type: 'SET_NOTIFICATIONS'; payload: any[] }
  | { type: 'SET_UNREAD_COUNT'; payload: number }
  | { type: 'ADD_NOTIFICATION'; payload: any }
  | { type: 'MARK_NOTIFICATION_READ'; payload: string }
  | { type: 'UPDATE_SETTINGS'; payload: Partial<AppState['settings']> }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_UI_LOADING'; payload: boolean }
  | { type: 'SET_UI_ERROR'; payload: string | null }
  | { type: 'SET_REALTIME_STATUS'; payload: Partial<AppState['realtime']> }

// Initial state
const initialState: AppState = {
  user: {
    data: null,
    loading: false,
    error: null
  },
  notifications: {
    items: [],
    unreadCount: 0,
    loading: false
  },
  settings: {
    theme: 'system',
    language: 'en',
    notifications: {
      email: true,
      push: true,
      sound: false
    },
    privacy: {
      showProfile: true,
      showStats: true,
      showAchievements: true
    }
  },
  ui: {
    sidebarOpen: false,
    loading: false,
    error: null
  },
  realtime: {
    connected: false,
    activeUsers: 0,
    systemStatus: 'online'
  }
}

// Reducer function
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_USER':
      return {
        ...state,
        user: {
          ...state.user,
          data: action.payload,
          loading: false,
          error: null
        }
      }
    
    case 'SET_USER_LOADING':
      return {
        ...state,
        user: {
          ...state.user,
          loading: action.payload
        }
      }
    
    case 'SET_USER_ERROR':
      return {
        ...state,
        user: {
          ...state.user,
          error: action.payload,
          loading: false
        }
      }
    
    case 'SET_NOTIFICATIONS':
      return {
        ...state,
        notifications: {
          ...state.notifications,
          items: action.payload,
          loading: false
        }
      }
    
    case 'SET_UNREAD_COUNT':
      return {
        ...state,
        notifications: {
          ...state.notifications,
          unreadCount: action.payload
        }
      }
    
    case 'ADD_NOTIFICATION':
      return {
        ...state,
        notifications: {
          ...state.notifications,
          items: [action.payload, ...state.notifications.items],
          unreadCount: state.notifications.unreadCount + 1
        }
      }
    
    case 'MARK_NOTIFICATION_READ':
      return {
        ...state,
        notifications: {
          ...state.notifications,
          items: state.notifications.items.map(item =>
            item.id === action.payload ? { ...item, isRead: true } : item
          ),
          unreadCount: Math.max(0, state.notifications.unreadCount - 1)
        }
      }
    
    case 'UPDATE_SETTINGS':
      return {
        ...state,
        settings: {
          ...state.settings,
          ...action.payload
        }
      }
    
    case 'TOGGLE_SIDEBAR':
      return {
        ...state,
        ui: {
          ...state.ui,
          sidebarOpen: !state.ui.sidebarOpen
        }
      }
    
    case 'SET_UI_LOADING':
      return {
        ...state,
        ui: {
          ...state.ui,
          loading: action.payload
        }
      }
    
    case 'SET_UI_ERROR':
      return {
        ...state,
        ui: {
          ...state.ui,
          error: action.payload
        }
      }
    
    case 'SET_REALTIME_STATUS':
      return {
        ...state,
        realtime: {
          ...state.realtime,
          ...action.payload
        }
      }
    
    default:
      return state
  }
}

// Context
const AppContext = createContext<{
  state: AppState
  dispatch: React.Dispatch<AppAction>
  actions: {
    // User actions
    fetchUserData: () => Promise<void>
    updateUserProfile: (data: any) => Promise<void>
    
    // Notification actions
    fetchNotifications: () => Promise<void>
    markNotificationAsRead: (id: string) => Promise<void>
    addNotification: (notification: any) => void
    
    // Settings actions
    updateSettings: (settings: Partial<AppState['settings']>) => Promise<void>
    
    // UI actions
    toggleSidebar: () => void
    setError: (error: string | null) => void
    
    // Realtime actions
    connectRealtime: () => void
    disconnectRealtime: () => void
  }
} | null>(null)

// Provider component
interface AppProviderProps {
  children: ReactNode
}

export function AppProvider({ children }: AppProviderProps) {
  const [state, dispatch] = useReducer(appReducer, initialState)
  const { data: session } = useSession()

  // Load user settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('app-settings')
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings)
        dispatch({ type: 'UPDATE_SETTINGS', payload: settings })
      } catch (error) {
        console.error('Failed to load settings:', error)
      }
    }
  }, [])

  // Save settings to localStorage when they change
  useEffect(() => {
    localStorage.setItem('app-settings', JSON.stringify(state.settings))
  }, [state.settings])

  // Fetch user data when session changes
  useEffect(() => {
    if (session?.user) {
      fetchUserData()
      fetchNotifications()
    }
  }, [session])

  // Actions
  const actions = {
    // User actions
    fetchUserData: async () => {
      if (!session?.user) return
      
      dispatch({ type: 'SET_USER_LOADING', payload: true })
      try {
        const response = await fetch('/api/user/profile')
        if (!response.ok) throw new Error('Failed to fetch user data')
        
        const data = await response.json()
        dispatch({ type: 'SET_USER', payload: data.data })
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch user data'
        dispatch({ type: 'SET_USER_ERROR', payload: errorMessage })
        toast.error(errorMessage)
      }
    },

    updateUserProfile: async (profileData: any) => {
      dispatch({ type: 'SET_USER_LOADING', payload: true })
      try {
        const response = await fetch('/api/user/profile', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(profileData)
        })
        
        if (!response.ok) throw new Error('Failed to update profile')
        
        const data = await response.json()
        dispatch({ type: 'SET_USER', payload: data.data })
        toast.success('Profile updated successfully')
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update profile'
        dispatch({ type: 'SET_USER_ERROR', payload: errorMessage })
        toast.error(errorMessage)
        throw error
      }
    },

    // Notification actions
    fetchNotifications: async () => {
      if (!session?.user) return
      
      try {
        const response = await fetch('/api/notifications')
        if (!response.ok) throw new Error('Failed to fetch notifications')
        
        const data = await response.json()
        dispatch({ type: 'SET_NOTIFICATIONS', payload: data.data })
        
        const unreadCount = data.data.filter((n: any) => !n.isRead).length
        dispatch({ type: 'SET_UNREAD_COUNT', payload: unreadCount })
      } catch (error) {
        console.error('Failed to fetch notifications:', error)
      }
    },

    markNotificationAsRead: async (id: string) => {
      try {
        const response = await fetch(`/api/notifications/${id}/read`, {
          method: 'POST'
        })
        
        if (!response.ok) throw new Error('Failed to mark notification as read')
        
        dispatch({ type: 'MARK_NOTIFICATION_READ', payload: id })
      } catch (error) {
        console.error('Failed to mark notification as read:', error)
      }
    },

    addNotification: (notification: any) => {
      dispatch({ type: 'ADD_NOTIFICATION', payload: notification })
    },

    // Settings actions
    updateSettings: async (settings: Partial<AppState['settings']>) => {
      dispatch({ type: 'UPDATE_SETTINGS', payload: settings })
      
      // Optionally sync with server
      try {
        await fetch('/api/user/settings', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(settings)
        })
      } catch (error) {
        console.error('Failed to sync settings with server:', error)
      }
    },

    // UI actions
    toggleSidebar: () => {
      dispatch({ type: 'TOGGLE_SIDEBAR' })
    },

    setError: (error: string | null) => {
      dispatch({ type: 'SET_UI_ERROR', payload: error })
    },

    // Realtime actions (placeholder implementations)
    connectRealtime: () => {
      dispatch({ type: 'SET_REALTIME_STATUS', payload: { connected: true } })
    },

    disconnectRealtime: () => {
      dispatch({ type: 'SET_REALTIME_STATUS', payload: { connected: false } })
    }
  }

  return (
    <AppContext.Provider value={{ state, dispatch, actions }}>
      {children}
    </AppContext.Provider>
  )
}

// Hook to use the app context
export function useApp() {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error('useApp must be used within an AppProvider')
  }
  return context
}

// Convenience hooks for specific parts of the state
export function useUser() {
  const { state, actions } = useApp()
  return {
    user: state.user.data,
    loading: state.user.loading,
    error: state.user.error,
    fetchUserData: actions.fetchUserData,
    updateUserProfile: actions.updateUserProfile
  }
}

export function useNotifications() {
  const { state, actions } = useApp()
  return {
    notifications: state.notifications.items,
    unreadCount: state.notifications.unreadCount,
    loading: state.notifications.loading,
    fetchNotifications: actions.fetchNotifications,
    markAsRead: actions.markNotificationAsRead,
    addNotification: actions.addNotification
  }
}

export function useSettings() {
  const { state, actions } = useApp()
  return {
    settings: state.settings,
    updateSettings: actions.updateSettings
  }
}

export function useUI() {
  const { state, actions } = useApp()
  return {
    sidebarOpen: state.ui.sidebarOpen,
    loading: state.ui.loading,
    error: state.ui.error,
    toggleSidebar: actions.toggleSidebar,
    setError: actions.setError
  }
}

export function useRealtime() {
  const { state, actions } = useApp()
  return {
    connected: state.realtime.connected,
    activeUsers: state.realtime.activeUsers,
    systemStatus: state.realtime.systemStatus,
    connect: actions.connectRealtime,
    disconnect: actions.disconnectRealtime
  }
}

// Theme-specific hook
export function useTheme() {
  const { state, actions } = useApp()

  const setTheme = (theme: 'light' | 'dark' | 'system') => {
    actions.updateSettings({ theme })

    // Apply theme to document
    const root = document.documentElement
    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
      root.classList.toggle('dark', systemTheme === 'dark')
    } else {
      root.classList.toggle('dark', theme === 'dark')
    }
  }

  return {
    theme: state.settings.theme,
    setTheme
  }
}
