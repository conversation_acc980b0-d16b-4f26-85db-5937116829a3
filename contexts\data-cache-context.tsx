"use client"

import { createContext, useContext, useReducer, useCallback, ReactNode } from 'react'

// ============================================================================
// DATA CACHE CONTEXT FOR OPTIMIZED DATA MANAGEMENT
// ============================================================================

interface CacheEntry<T = any> {
  data: T
  timestamp: number
  expiry?: number
  loading?: boolean
  error?: string | null
}

interface CacheState {
  [key: string]: CacheEntry
}

type CacheAction =
  | { type: 'SET_DATA'; key: string; data: any; expiry?: number }
  | { type: 'SET_LOADING'; key: string; loading: boolean }
  | { type: 'SET_ERROR'; key: string; error: string | null }
  | { type: 'INVALIDATE'; key: string }
  | { type: 'CLEAR_ALL' }
  | { type: 'CLEAR_EXPIRED' }

const initialState: CacheState = {}

function cacheReducer(state: CacheState, action: CacheAction): CacheState {
  switch (action.type) {
    case 'SET_DATA':
      return {
        ...state,
        [action.key]: {
          data: action.data,
          timestamp: Date.now(),
          expiry: action.expiry,
          loading: false,
          error: null
        }
      }
    
    case 'SET_LOADING':
      return {
        ...state,
        [action.key]: {
          ...state[action.key],
          loading: action.loading
        }
      }
    
    case 'SET_ERROR':
      return {
        ...state,
        [action.key]: {
          ...state[action.key],
          error: action.error,
          loading: false
        }
      }
    
    case 'INVALIDATE':
      const { [action.key]: removed, ...rest } = state
      return rest
    
    case 'CLEAR_ALL':
      return {}
    
    case 'CLEAR_EXPIRED':
      const now = Date.now()
      const filtered: CacheState = {}
      
      Object.entries(state).forEach(([key, entry]) => {
        if (!entry.expiry || entry.timestamp + entry.expiry > now) {
          filtered[key] = entry
        }
      })
      
      return filtered
    
    default:
      return state
  }
}

interface DataCacheContextType {
  // Core cache operations
  get: <T = any>(key: string) => CacheEntry<T> | undefined
  set: <T = any>(key: string, data: T, expiry?: number) => void
  invalidate: (key: string) => void
  clear: () => void
  
  // Fetch with cache
  fetchWithCache: <T = any>(
    key: string,
    fetcher: () => Promise<T>,
    options?: {
      expiry?: number
      forceRefresh?: boolean
      onError?: (error: Error) => void
    }
  ) => Promise<T>
  
  // Batch operations
  invalidatePattern: (pattern: RegExp) => void
  prefetch: <T = any>(key: string, fetcher: () => Promise<T>, expiry?: number) => void
  
  // Cache status
  isLoading: (key: string) => boolean
  hasError: (key: string) => boolean
  getError: (key: string) => string | null
  isExpired: (key: string) => boolean
  
  // Cache statistics
  getStats: () => {
    totalEntries: number
    expiredEntries: number
    loadingEntries: number
    errorEntries: number
  }
}

const DataCacheContext = createContext<DataCacheContextType | null>(null)

interface DataCacheProviderProps {
  children: ReactNode
  defaultExpiry?: number // Default expiry time in milliseconds
}

export function DataCacheProvider({ 
  children, 
  defaultExpiry = 5 * 60 * 1000 // 5 minutes default
}: DataCacheProviderProps) {
  const [state, dispatch] = useReducer(cacheReducer, initialState)

  // Core cache operations
  const get = useCallback(<T = any>(key: string): CacheEntry<T> | undefined => {
    return state[key] as CacheEntry<T> | undefined
  }, [state])

  const set = useCallback(<T = any>(key: string, data: T, expiry?: number) => {
    dispatch({
      type: 'SET_DATA',
      key,
      data,
      expiry: expiry || defaultExpiry
    })
  }, [defaultExpiry])

  const invalidate = useCallback((key: string) => {
    dispatch({ type: 'INVALIDATE', key })
  }, [])

  const clear = useCallback(() => {
    dispatch({ type: 'CLEAR_ALL' })
  }, [])

  // Fetch with cache
  const fetchWithCache = useCallback(async <T = any>(
    key: string,
    fetcher: () => Promise<T>,
    options: {
      expiry?: number
      forceRefresh?: boolean
      onError?: (error: Error) => void
    } = {}
  ): Promise<T> => {
    const { expiry, forceRefresh = false, onError } = options
    const cached = get<T>(key)
    
    // Return cached data if valid and not forcing refresh
    if (!forceRefresh && cached && !isExpired(key) && !cached.error) {
      return cached.data
    }
    
    // Set loading state
    dispatch({ type: 'SET_LOADING', key, loading: true })
    
    try {
      const data = await fetcher()
      set(key, data, expiry)
      return data
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      dispatch({ type: 'SET_ERROR', key, error: errorMessage })
      
      if (onError) {
        onError(error as Error)
      }
      
      // Return cached data if available, even if expired
      if (cached?.data) {
        return cached.data
      }
      
      throw error
    }
  }, [get, set, defaultExpiry])

  // Batch operations
  const invalidatePattern = useCallback((pattern: RegExp) => {
    Object.keys(state).forEach(key => {
      if (pattern.test(key)) {
        invalidate(key)
      }
    })
  }, [state, invalidate])

  const prefetch = useCallback(<T = any>(
    key: string, 
    fetcher: () => Promise<T>, 
    expiry?: number
  ) => {
    // Only prefetch if not already cached or expired
    if (!get(key) || isExpired(key)) {
      fetchWithCache(key, fetcher, { expiry }).catch(() => {
        // Silently handle prefetch errors
      })
    }
  }, [get, fetchWithCache])

  // Cache status helpers
  const isLoading = useCallback((key: string): boolean => {
    return state[key]?.loading || false
  }, [state])

  const hasError = useCallback((key: string): boolean => {
    return !!state[key]?.error
  }, [state])

  const getError = useCallback((key: string): string | null => {
    return state[key]?.error || null
  }, [state])

  const isExpired = useCallback((key: string): boolean => {
    const entry = state[key]
    if (!entry || !entry.expiry) return false
    
    return Date.now() > entry.timestamp + entry.expiry
  }, [state])

  // Cache statistics
  const getStats = useCallback(() => {
    const entries = Object.values(state)
    const now = Date.now()
    
    return {
      totalEntries: entries.length,
      expiredEntries: entries.filter(entry => 
        entry.expiry && now > entry.timestamp + entry.expiry
      ).length,
      loadingEntries: entries.filter(entry => entry.loading).length,
      errorEntries: entries.filter(entry => entry.error).length
    }
  }, [state])

  // Auto-cleanup expired entries periodically
  useCallback(() => {
    const interval = setInterval(() => {
      dispatch({ type: 'CLEAR_EXPIRED' })
    }, 60000) // Clean up every minute
    
    return () => clearInterval(interval)
  }, [])

  const contextValue: DataCacheContextType = {
    get,
    set,
    invalidate,
    clear,
    fetchWithCache,
    invalidatePattern,
    prefetch,
    isLoading,
    hasError,
    getError,
    isExpired,
    getStats
  }

  return (
    <DataCacheContext.Provider value={contextValue}>
      {children}
    </DataCacheContext.Provider>
  )
}

// Hook to use the data cache
export function useDataCache() {
  const context = useContext(DataCacheContext)
  if (!context) {
    throw new Error('useDataCache must be used within a DataCacheProvider')
  }
  return context
}

// Specialized hooks for common patterns
export function useCachedData<T = any>(
  key: string,
  fetcher: () => Promise<T>,
  options?: {
    expiry?: number
    enabled?: boolean
    onError?: (error: Error) => void
  }
) {
  const cache = useDataCache()
  const { enabled = true, ...fetchOptions } = options || {}
  
  const cached = cache.get<T>(key)
  const loading = cache.isLoading(key)
  const error = cache.getError(key)
  
  // Fetch data if enabled and not already cached/loading
  if (enabled && !cached && !loading) {
    cache.fetchWithCache(key, fetcher, fetchOptions).catch(() => {
      // Error is handled by the cache
    })
  }
  
  const refetch = useCallback(() => {
    if (enabled) {
      return cache.fetchWithCache(key, fetcher, { ...fetchOptions, forceRefresh: true })
    }
    return Promise.resolve(cached?.data)
  }, [cache, key, fetcher, fetchOptions, enabled, cached])
  
  const invalidate = useCallback(() => {
    cache.invalidate(key)
  }, [cache, key])
  
  return {
    data: cached?.data,
    loading,
    error,
    refetch,
    invalidate,
    isExpired: cache.isExpired(key)
  }
}

// Hook for managing lists with cache
export function useCachedList<T = any>(
  baseKey: string,
  fetcher: (params: any) => Promise<{ data: T[]; pagination?: any }>,
  params: any = {}
) {
  const cache = useDataCache()
  const key = `${baseKey}:${JSON.stringify(params)}`
  
  return useCachedData(key, () => fetcher(params), {
    onError: (error) => {
      console.error(`Failed to fetch ${baseKey}:`, error)
    }
  })
}

// Hook for managing single items with cache
export function useCachedItem<T = any>(
  baseKey: string,
  id: string,
  fetcher: (id: string) => Promise<T>,
  options?: { enabled?: boolean }
) {
  const cache = useDataCache()
  const key = `${baseKey}:${id}`
  const { enabled = !!id } = options || {}
  
  return useCachedData(key, () => fetcher(id), {
    enabled,
    onError: (error) => {
      console.error(`Failed to fetch ${baseKey} ${id}:`, error)
    }
  })
}
