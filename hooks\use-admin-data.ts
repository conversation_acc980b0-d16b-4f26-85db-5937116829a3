import { useState, useEffect, useCallback } from 'react'
import { toast } from 'sonner'

// ============================================================================
// SHARED ADMIN DATA HOOKS
// ============================================================================

// Generic pagination hook
export interface PaginationState {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

export interface UseDataFetchingOptions {
  endpoint: string
  initialParams?: Record<string, any>
  autoFetch?: boolean
  onSuccess?: (data: any) => void
  onError?: (error: Error) => void
}

export function useDataFetching<T = any>({
  endpoint,
  initialParams = {},
  autoFetch = true,
  onSuccess,
  onError
}: UseDataFetchingOptions) {
  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [params, setParams] = useState(initialParams)

  const fetchData = useCallback(async (customParams?: Record<string, any>) => {
    try {
      setLoading(true)
      setError(null)
      
      const queryParams = new URLSearchParams({
        ...params,
        ...customParams
      })
      
      const response = await fetch(`${endpoint}?${queryParams}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch data')
      }
      
      const result = await response.json()
      setData(result.data)
      onSuccess?.(result.data)
      
      return result.data
    } catch (err) {
      const error = err as Error
      setError(error.message)
      onError?.(error)
      toast.error(error.message)
      throw error
    } finally {
      setLoading(false)
    }
  }, [endpoint, params, onSuccess, onError])

  const updateParams = useCallback((newParams: Record<string, any>) => {
    setParams(prev => ({ ...prev, ...newParams }))
  }, [])

  const refresh = useCallback(() => {
    return fetchData()
  }, [fetchData])

  useEffect(() => {
    if (autoFetch) {
      fetchData()
    }
  }, [fetchData, autoFetch])

  return {
    data,
    loading,
    error,
    params,
    fetchData,
    updateParams,
    refresh,
    setData
  }
}

// Paginated data fetching hook
export function usePaginatedData<T = any>({
  endpoint,
  initialParams = {},
  autoFetch = true,
  onSuccess,
  onError
}: UseDataFetchingOptions) {
  const [items, setItems] = useState<T[]>([])
  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })

  const {
    data,
    loading,
    error,
    params,
    fetchData,
    updateParams,
    refresh
  } = useDataFetching({
    endpoint,
    initialParams: { page: 1, limit: 10, ...initialParams },
    autoFetch,
    onSuccess: (data) => {
      if (data.pagination) {
        setItems(data.data || data.items || data)
        setPagination(data.pagination)
      } else {
        setItems(Array.isArray(data) ? data : [data])
      }
      onSuccess?.(data)
    },
    onError
  })

  const goToPage = useCallback((page: number) => {
    updateParams({ page })
  }, [updateParams])

  const changeLimit = useCallback((limit: number) => {
    updateParams({ limit, page: 1 })
  }, [updateParams])

  const search = useCallback((query: string) => {
    updateParams({ search: query, page: 1 })
  }, [updateParams])

  const filter = useCallback((filters: Record<string, any>) => {
    updateParams({ ...filters, page: 1 })
  }, [updateParams])

  return {
    items,
    pagination,
    loading,
    error,
    params,
    fetchData,
    updateParams,
    refresh,
    goToPage,
    changeLimit,
    search,
    filter,
    setItems
  }
}

// Dashboard stats hook
export function useDashboardStats() {
  return useDataFetching({
    endpoint: '/api/admin/dashboard/stats',
    onError: (error) => {
      console.error('Failed to fetch dashboard stats:', error)
    }
  })
}

// Users management hook
export function useUsersData() {
  const {
    items: users,
    pagination,
    loading,
    error,
    refresh,
    search,
    filter,
    goToPage,
    setItems
  } = usePaginatedData({
    endpoint: '/api/admin/users'
  })

  const createUser = useCallback(async (userData: any) => {
    try {
      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create user')
      }

      const result = await response.json()
      setItems(prev => [result.data, ...prev])
      toast.success('User created successfully')
      return result.data
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [setItems])

  const updateUser = useCallback(async (userId: string, userData: any) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update user')
      }

      const result = await response.json()
      setItems(prev => prev.map(user => 
        user.id === userId ? { ...user, ...result.data } : user
      ))
      toast.success('User updated successfully')
      return result.data
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [setItems])

  const deleteUser = useCallback(async (userId: string) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete user')
      }

      setItems(prev => prev.filter(user => user.id !== userId))
      toast.success('User deleted successfully')
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [setItems])

  return {
    users,
    pagination,
    loading,
    error,
    refresh,
    search,
    filter,
    goToPage,
    createUser,
    updateUser,
    deleteUser
  }
}

// Quiz management hook
export function useQuizzesData() {
  const {
    items: quizzes,
    pagination,
    loading,
    error,
    refresh,
    search,
    filter,
    goToPage,
    setItems
  } = usePaginatedData({
    endpoint: '/api/admin/quizzes'
  })

  const createQuiz = useCallback(async (quizData: any) => {
    try {
      const response = await fetch('/api/admin/quizzes', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(quizData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create quiz')
      }

      const result = await response.json()
      setItems(prev => [result.data, ...prev])
      toast.success('Quiz created successfully')
      return result.data
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [setItems])

  const updateQuiz = useCallback(async (quizId: string, quizData: any) => {
    try {
      const response = await fetch(`/api/admin/quizzes/${quizId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(quizData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update quiz')
      }

      const result = await response.json()
      setItems(prev => prev.map(quiz => 
        quiz.id === quizId ? { ...quiz, ...result.data } : quiz
      ))
      toast.success('Quiz updated successfully')
      return result.data
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [setItems])

  const deleteQuiz = useCallback(async (quizId: string) => {
    try {
      const response = await fetch(`/api/admin/quizzes/${quizId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete quiz')
      }

      setItems(prev => prev.filter(quiz => quiz.id !== quizId))
      toast.success('Quiz deleted successfully')
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [setItems])

  return {
    quizzes,
    pagination,
    loading,
    error,
    refresh,
    search,
    filter,
    goToPage,
    createQuiz,
    updateQuiz,
    deleteQuiz
  }
}

// Analytics data hook
export function useAnalyticsData(period: string = '30d') {
  return useDataFetching({
    endpoint: '/api/admin/analytics',
    initialParams: { period },
    onError: (error) => {
      console.error('Failed to fetch analytics data:', error)
    }
  })
}

// Settings management hook
export function useSettingsData() {
  const [saving, setSaving] = useState(false)
  
  const {
    data: settings,
    loading,
    error,
    refresh,
    setData
  } = useDataFetching({
    endpoint: '/api/admin/settings'
  })

  const updateSettings = useCallback(async (settingsData: any) => {
    try {
      setSaving(true)
      const response = await fetch('/api/admin/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settingsData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update settings')
      }

      const result = await response.json()
      setData(result.data)
      toast.success('Settings updated successfully')
      return result.data
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    } finally {
      setSaving(false)
    }
  }, [setData])

  return {
    settings,
    loading,
    error,
    saving,
    refresh,
    updateSettings
  }
}

// File management hook
export function useFilesData() {
  const {
    items: files,
    pagination,
    loading,
    error,
    refresh,
    search,
    filter,
    goToPage,
    setItems
  } = usePaginatedData({
    endpoint: '/api/admin/files'
  })

  const uploadFile = useCallback(async (fileData: any) => {
    try {
      const response = await fetch('/api/admin/files', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(fileData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to upload file')
      }

      const result = await response.json()
      setItems(prev => [result.data, ...prev])
      toast.success('File uploaded successfully')
      return result.data
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [setItems])

  const deleteFile = useCallback(async (fileId: string) => {
    try {
      const response = await fetch(`/api/admin/files/${fileId}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to delete file')
      }

      setItems(prev => prev.filter(file => file.id !== fileId))
      toast.success('File deleted successfully')
    } catch (error) {
      const err = error as Error
      toast.error(err.message)
      throw error
    }
  }, [setItems])

  return {
    files,
    pagination,
    loading,
    error,
    refresh,
    search,
    filter,
    goToPage,
    uploadFile,
    deleteFile
  }
}

// Generic form handling hook
export function useFormHandler<T = any>(initialData: T) {
  const [formData, setFormData] = useState<T>(initialData)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})

  const updateField = useCallback((field: keyof T, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field as string]) {
      setErrors(prev => ({ ...prev, [field as string]: '' }))
    }
  }, [errors])

  const touchField = useCallback((field: keyof T) => {
    setTouched(prev => ({ ...prev, [field as string]: true }))
  }, [])

  const setFieldError = useCallback((field: keyof T, error: string) => {
    setErrors(prev => ({ ...prev, [field as string]: error }))
  }, [])

  const resetForm = useCallback(() => {
    setFormData(initialData)
    setErrors({})
    setTouched({})
  }, [initialData])

  const isFieldTouched = useCallback((field: keyof T) => {
    return touched[field as string] || false
  }, [touched])

  const getFieldError = useCallback((field: keyof T) => {
    return errors[field as string] || ''
  }, [errors])

  return {
    formData,
    errors,
    touched,
    updateField,
    touchField,
    setFieldError,
    resetForm,
    isFieldTouched,
    getFieldError,
    setFormData,
    setErrors
  }
}
