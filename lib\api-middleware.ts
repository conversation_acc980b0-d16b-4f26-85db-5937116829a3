import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { z } from 'zod'
import {
  commonSchemas,
  apiSuccessSchema,
  apiErrorSchema,
  paginatedApiResponseSchema,
  bulkOperationRequestSchema,
  bulkOperationResultSchema
} from '@/lib/schemas'

export interface APIMiddlewareConfig {
  requireAuth?: boolean
  requireRole?: 'ADMIN' | 'STUDENT'
  validateBody?: z.ZodSchema
  validateQuery?: z.ZodSchema
  validateParams?: z.ZodSchema
  rateLimit?: {
    requests: number
    windowMs: number
  }
  cache?: {
    ttl: number
    key?: string
  }
  cors?: {
    origin?: string | string[]
    methods?: string[]
    headers?: string[]
  }
  logging?: boolean
}

export class APIError extends Error {
  constructor(
    public message: string,
    public statusCode: number = 500,
    public code?: string
  ) {
    super(message)
    this.name = 'APIError'
  }
}

export class APIResponse {
  static success(data: any, message?: string, statusCode: number = 200) {
    return NextResponse.json({
      success: true,
      data,
      message,
      timestamp: new Date().toISOString()
    }, { status: statusCode })
  }

  static error(
    message: string,
    statusCode: number = 500,
    code?: string,
    details?: any
  ) {
    return NextResponse.json({
      success: false,
      error: message,
      code,
      details,
      timestamp: new Date().toISOString()
    }, { status: statusCode })
  }

  static paginated(
    data: any[],
    pagination: {
      page: number
      limit: number
      total: number
      totalPages: number
      hasNext: boolean
      hasPrev: boolean
    },
    message?: string
  ) {
    return NextResponse.json({
      success: true,
      data,
      pagination,
      message,
      timestamp: new Date().toISOString()
    })
  }

  // Common HTTP status responses
  static unauthorized(message: string = 'Unauthorized') {
    return this.error(message, 401, 'UNAUTHORIZED')
  }

  static forbidden(message: string = 'Forbidden') {
    return this.error(message, 403, 'FORBIDDEN')
  }

  static notFound(message: string = 'Not found') {
    return this.error(message, 404, 'NOT_FOUND')
  }

  static badRequest(message: string = 'Bad request', details?: any) {
    return this.error(message, 400, 'BAD_REQUEST', details)
  }

  static conflict(message: string = 'Conflict') {
    return this.error(message, 409, 'CONFLICT')
  }

  static unprocessableEntity(message: string = 'Unprocessable Entity', details?: any) {
    return this.error(message, 422, 'UNPROCESSABLE_ENTITY', details)
  }

  static tooManyRequests(message: string = 'Too Many Requests') {
    return this.error(message, 429, 'TOO_MANY_REQUESTS')
  }

  static internalServerError(message: string = 'Internal Server Error') {
    return this.error(message, 500, 'INTERNAL_SERVER_ERROR')
  }

  static serviceUnavailable(message: string = 'Service Unavailable') {
    return this.error(message, 503, 'SERVICE_UNAVAILABLE')
  }

  // Specialized responses
  static created(data: any, message?: string) {
    return this.success(data, message, 201)
  }

  static accepted(data: any, message?: string) {
    return this.success(data, message, 202)
  }

  static noContent(message?: string) {
    return NextResponse.json({
      success: true,
      message,
      timestamp: new Date().toISOString()
    }, { status: 204 })
  }

  static bulkResult(results: Array<{ id: string; success: boolean; error?: string }>) {
    const total = results.length
    const successful = results.filter(r => r.success).length
    const failed = total - successful

    return this.success({
      total,
      successful,
      failed,
      results
    })
  }

  // Validation error helper
  static validationError(errors: z.ZodError) {
    return this.error(
      'Validation failed',
      400,
      'VALIDATION_ERROR',
      errors.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code
      }))
    )
  }
}

export function createAPIHandler(
  config: APIMiddlewareConfig,
  handler: (request: NextRequest, context: any) => Promise<NextResponse>
) {
  return async (request: NextRequest, context?: any) => {
    const startTime = Date.now()
    let response: NextResponse

    try {
      // Import utilities
      const {
        checkRateLimit,
        getClientIP,
        getUserAgent,
        getRequestId,
        parseQueryParams,
        getCachedData,
        setCachedData
      } = await import('@/lib/api-utils')

      const requestId = getRequestId(request)
      const clientIP = getClientIP(request)

      // Logging
      if (config.logging !== false) {
        console.log(`[${requestId}] ${request.method} ${request.url} - ${clientIP}`)
      }

      // Rate limiting
      if (config.rateLimit) {
        const identifier = `${clientIP}:${request.url}`
        const rateCheck = checkRateLimit(
          identifier,
          config.rateLimit.requests,
          config.rateLimit.windowMs
        )

        if (!rateCheck.allowed) {
          return APIResponse.tooManyRequests('Rate limit exceeded')
        }
      }

      // Cache check (for GET requests)
      if (request.method === 'GET' && config.cache) {
        const cacheKey = config.cache.key || `${request.method}:${request.url}`
        const cachedData = getCachedData(cacheKey)

        if (cachedData) {
          return APIResponse.success(cachedData, 'From cache')
        }
      }

      // Authentication
      let session = null
      if (config.requireAuth || config.requireRole) {
        session = await auth()
        if (!session?.user?.id) {
          return APIResponse.unauthorized('Authentication required')
        }

        // Role-based access control
        if (config.requireRole && session.user.role !== config.requireRole) {
          return APIResponse.forbidden(`${config.requireRole} role required`)
        }
      }

      // Parameter validation
      let validatedParams = null
      if (config.validateParams && context?.params) {
        try {
          const params = await context.params
          validatedParams = config.validateParams.parse(params)
        } catch (error) {
          if (error instanceof z.ZodError) {
            return APIResponse.validationError(error)
          }
        }
      }

      // Request body validation
      let validatedBody = null
      if (config.validateBody && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
        try {
          const body = await request.json()
          validatedBody = config.validateBody.parse(body)
        } catch (error) {
          if (error instanceof z.ZodError) {
            return APIResponse.validationError(error)
          }
          return APIResponse.badRequest('Invalid JSON in request body')
        }
      }

      // Query parameters validation
      let validatedQuery = null
      if (config.validateQuery) {
        try {
          const queryParams = parseQueryParams(request)
          validatedQuery = config.validateQuery.parse(queryParams)
        } catch (error) {
          if (error instanceof z.ZodError) {
            return APIResponse.validationError(error)
          }
        }
      }

      // Create enhanced context
      const enhancedContext = {
        ...context,
        session,
        validatedBody,
        validatedQuery,
        validatedParams,
        user: session?.user,
        requestId,
        clientIP,
        userAgent: getUserAgent(request)
      }

      // Call the actual handler
      response = await handler(request, enhancedContext)

      // Cache response (for successful GET requests)
      if (request.method === 'GET' && config.cache && response.status === 200) {
        const cacheKey = config.cache.key || `${request.method}:${request.url}`
        const responseData = await response.clone().json()
        setCachedData(cacheKey, responseData, config.cache.ttl)
      }

      // Add CORS headers if configured
      if (config.cors) {
        const { setCORSHeaders } = await import('@/lib/api-utils')
        response = setCORSHeaders(response, config.cors.origin?.[0])
      }

      return response

    } catch (error) {
      console.error(`[${getRequestId(request)}] API Handler Error:`, error)

      if (error instanceof APIError) {
        return APIResponse.error(error.message, error.statusCode, error.code)
      }

      if (error instanceof z.ZodError) {
        return APIResponse.validationError(error)
      }

      // Generic error
      return APIResponse.internalServerError('An unexpected error occurred')
    } finally {
      const duration = Date.now() - startTime
      if (config.logging !== false) {
        console.log(`[${getRequestId(request)}] Completed in ${duration}ms`)
      }
    }
  }
}



// Common validation schemas
export const commonSchemas = {
  pagination: z.object({
    page: z.string().optional().transform(val => parseInt(val || '1') || 1),
    limit: z.string().optional().transform(val => Math.min(parseInt(val || '20') || 20, 100)),
    sort: z.string().optional(),
    order: z.enum(['asc', 'desc']).optional()
  }),

  search: z.object({
    q: z.string().optional(),
    category: z.string().optional(),
    tags: z.string().optional(),
    status: z.string().optional()
  }),

  idParam: z.object({
    id: z.string().min(1, 'ID is required')
  })
}



// API versioning helper
export function getAPIVersion(request: NextRequest): string {
  const version = request.headers.get('api-version') || 
                 request.nextUrl.searchParams.get('version') ||
                 'v1'
  return version
}

// CORS helper
export function setCORSHeaders(response: NextResponse, origin?: string): NextResponse {
  if (origin) {
    response.headers.set('Access-Control-Allow-Origin', origin)
  }
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, API-Version')
  response.headers.set('Access-Control-Max-Age', '86400')
  
  return response
}

// Request logging
export function logAPIRequest(request: NextRequest, response?: NextResponse) {
  const timestamp = new Date().toISOString()
  const method = request.method
  const url = request.url
  const userAgent = request.headers.get('user-agent') || 'unknown'
  const ip = request.headers.get('x-forwarded-for') || 'unknown'
  const status = response?.status || 'pending'

  console.log(`[${timestamp}] ${method} ${url} - ${status} - ${ip} - ${userAgent}`)
}

// Health check endpoint helper
export function createHealthCheck() {
  return APIResponse.success({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.APP_VERSION || '1.0.0',
    environment: process.env.NODE_ENV || 'development'
  })
}
