// ============================================================================
// SHARED UI COMPONENTS LIBRARY
// ============================================================================
// This file provides centralized exports for all shared UI components
// to eliminate redundancy across admin and student interfaces

// Form components
export {
  FormLayout,
  FormSection,
  FormActions,
  SubmitButton,
  CancelButton,
  FormField,
  FormGrid,
  FormSteps
} from "./form-layout"

// Loading state components
export {
  LoadingSpinner,
  LoadingOverlay,
  LoadingCard,
  LoadingTable,
  LoadingList,
  LoadingButton,
  InlineLoading,
  ProgressLoading,
  RefreshButton,
  LoadingPage,
  SkeletonCard
} from "./loading-states"

// Error state components
export {
  ErrorMessage,
  ErrorCard,
  ErrorPage,
  NetworkError,
  ValidationError,
  ErrorBoundaryFallback,
  InlineError
} from "./error-states"

// Card layout components
export {
  BaseCard,
  StatsCard,
  ActionCard,
  ListCard,
  FeatureCard,
  GridLayout
} from "./card-layouts"

// Common component patterns and utilities
export const commonPatterns = {
  // Form patterns
  createFormField: (label: string, required = false) => ({
    label,
    required,
    className: "space-y-2"
  }),

  // Loading patterns
  createLoadingState: (message = "Loading...") => ({
    loading: true,
    message,
    spinnerSize: "md" as const
  }),

  // Error patterns
  createErrorState: (message: string, onRetry?: () => void) => ({
    error: true,
    message,
    onRetry
  }),

  // Card patterns
  createStatsCard: (title: string, value: string | number) => ({
    title,
    value,
    className: "w-full"
  })
}

// Component composition helpers
export const withLoading = <T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  loadingComponent?: React.ComponentType
) => {
  return (props: T & { loading?: boolean }) => {
    if (props.loading) {
      return loadingComponent ? React.createElement(loadingComponent) : React.createElement(LoadingSpinner)
    }
    return React.createElement(Component, props)
  }
}

export const withError = <T extends Record<string, any>>(
  Component: React.ComponentType<T>,
  errorComponent?: React.ComponentType<{ error: any; onRetry?: () => void }>
) => {
  return (props: T & { error?: any; onRetry?: () => void }) => {
    if (props.error) {
      return errorComponent 
        ? React.createElement(errorComponent, { error: props.error, onRetry: props.onRetry })
        : React.createElement(ErrorMessage, { message: props.error.message || "An error occurred" })
    }
    return React.createElement(Component, props)
  }
}

// Common prop types for consistency
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface LoadingProps {
  loading?: boolean
  loadingMessage?: string
}

export interface ErrorProps {
  error?: Error | string | null
  onRetry?: () => void
}

export interface ActionProps {
  onSubmit?: () => void
  onCancel?: () => void
  onRetry?: () => void
  disabled?: boolean
}

// Utility functions for common UI patterns
export const createFormActions = (
  onSubmit?: () => void,
  onCancel?: () => void,
  options?: {
    submitLabel?: string
    cancelLabel?: string
    loading?: boolean
    disabled?: boolean
  }
) => ({
  onSubmit,
  onCancel,
  submitLabel: options?.submitLabel || "Submit",
  cancelLabel: options?.cancelLabel || "Cancel",
  loading: options?.loading || false,
  disabled: options?.disabled || false
})

export const createCardGrid = (
  columns: 1 | 2 | 3 | 4 = 3,
  gap: "sm" | "md" | "lg" = "md"
) => ({
  columns,
  gap,
  className: "w-full"
})

// Theme-aware component variants
export const componentVariants = {
  card: {
    default: "border bg-card text-card-foreground shadow-sm",
    elevated: "border bg-card text-card-foreground shadow-md",
    flat: "border-0 bg-muted/50 text-card-foreground"
  },
  button: {
    primary: "bg-primary text-primary-foreground hover:bg-primary/90",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
    success: "bg-green-600 text-white hover:bg-green-700",
    warning: "bg-yellow-600 text-white hover:bg-yellow-700",
    danger: "bg-red-600 text-white hover:bg-red-700"
  },
  alert: {
    info: "border-blue-200 bg-blue-50 text-blue-800",
    success: "border-green-200 bg-green-50 text-green-800",
    warning: "border-yellow-200 bg-yellow-50 text-yellow-800",
    error: "border-red-200 bg-red-50 text-red-800"
  }
}

// Responsive breakpoint helpers
export const breakpoints = {
  sm: "640px",
  md: "768px",
  lg: "1024px",
  xl: "1280px",
  "2xl": "1536px"
}

export const responsiveClasses = {
  hide: {
    sm: "hidden sm:block",
    md: "hidden md:block",
    lg: "hidden lg:block",
    xl: "hidden xl:block"
  },
  show: {
    sm: "block sm:hidden",
    md: "block md:hidden",
    lg: "block lg:hidden",
    xl: "block xl:hidden"
  }
}

// Animation presets
export const animations = {
  fadeIn: "animate-in fade-in duration-200",
  fadeOut: "animate-out fade-out duration-200",
  slideIn: "animate-in slide-in-from-bottom duration-300",
  slideOut: "animate-out slide-out-to-bottom duration-300",
  scaleIn: "animate-in zoom-in-95 duration-200",
  scaleOut: "animate-out zoom-out-95 duration-200"
}

// Common spacing values
export const spacing = {
  xs: "0.25rem",
  sm: "0.5rem",
  md: "1rem",
  lg: "1.5rem",
  xl: "2rem",
  "2xl": "3rem"
}

// Import components for composition helpers
import React from "react"
import { LoadingSpinner } from "./loading-states"
import { ErrorMessage } from "./error-states"
export { React }
