"use client"

import { ReactNode } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { Menu } from "lucide-react"
import { cn } from "@/lib/utils"
import { MobileSidebarContent } from "./base-sidebar"
import { NavigationItemData } from "./navigation-item"
import { LucideIcon } from "lucide-react"

interface BaseHeaderProps {
  // Mobile sidebar props
  logoHref: string
  logoIcon: LucideIcon
  logoTitle: string
  logoSubtitle: string
  navigationItems: NavigationItemData[]
  userData?: any
  userLoading?: boolean
  showUserStats?: boolean
  userProfileHref?: string
  userSettingsHref?: string
  
  // Header content
  leftContent?: ReactNode
  centerContent?: ReactNode
  rightContent?: ReactNode
  
  // Styling
  className?: string
  backgroundColor?: string
  sticky?: boolean
  
  // Mobile sidebar customization
  mobileSheetSide?: "left" | "right"
  mobileSheetWidth?: string
  showMobileSidebar?: boolean
  
  // Additional mobile sidebar content
  mobileSidebarHeaderContent?: ReactNode
  mobileSidebarFooterContent?: ReactNode
}

export function BaseHeader({
  logoHref,
  logoIcon,
  logoTitle,
  logoSubtitle,
  navigationItems,
  userData,
  userLoading = false,
  showUserStats = false,
  userProfileHref,
  userSettingsHref,
  leftContent,
  centerContent,
  rightContent,
  className,
  backgroundColor = "bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
  sticky = true,
  mobileSheetSide = "left",
  mobileSheetWidth = "w-80",
  showMobileSidebar = true,
  mobileSidebarHeaderContent,
  mobileSidebarFooterContent
}: BaseHeaderProps) {
  const headerClasses = cn(
    "border-b",
    sticky && "sticky top-0 z-40",
    backgroundColor,
    className
  )

  return (
    <header className={headerClasses}>
      <div className="flex h-16 items-center gap-4 px-4 md:px-6">
        {/* Mobile Menu Button */}
        {showMobileSidebar && (
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="md:hidden">
                <Menu className="h-4 w-4" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side={mobileSheetSide} className={mobileSheetWidth}>
              <MobileSidebarContent
                logoHref={logoHref}
                logoIcon={logoIcon}
                logoTitle={logoTitle}
                logoSubtitle={logoSubtitle}
                navigationItems={navigationItems}
                userData={userData}
                userLoading={userLoading}
                showUserStats={showUserStats}
                userProfileHref={userProfileHref}
                userSettingsHref={userSettingsHref}
                headerContent={mobileSidebarHeaderContent}
                footerContent={mobileSidebarFooterContent}
              />
            </SheetContent>
          </Sheet>
        )}

        {/* Left Content */}
        {leftContent && (
          <div className="flex items-center gap-4">
            {leftContent}
          </div>
        )}

        {/* Center Content */}
        {centerContent && (
          <div className="flex-1 flex items-center justify-center">
            {centerContent}
          </div>
        )}

        {/* Right Content */}
        <div className="flex items-center gap-4 ml-auto">
          {rightContent}
        </div>
      </div>
    </header>
  )
}

// Specialized header variants for common use cases
interface SearchHeaderProps extends Omit<BaseHeaderProps, 'centerContent'> {
  searchPlaceholder?: string
  onSearch?: (query: string) => void
  searchValue?: string
}

export function SearchHeader({
  searchPlaceholder = "Search...",
  onSearch,
  searchValue,
  ...props
}: SearchHeaderProps) {
  return (
    <BaseHeader
      {...props}
      centerContent={
        <div className="hidden md:flex items-center gap-4 max-w-md w-full">
          <div className="relative flex-1">
            <input
              type="text"
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => onSearch?.(e.target.value)}
              className="w-full px-3 py-2 text-sm border rounded-md bg-background"
            />
          </div>
        </div>
      }
    />
  )
}

interface BreadcrumbHeaderProps extends Omit<BaseHeaderProps, 'leftContent'> {
  breadcrumbs: Array<{
    label: string
    href?: string
  }>
}

export function BreadcrumbHeader({
  breadcrumbs,
  ...props
}: BreadcrumbHeaderProps) {
  return (
    <BaseHeader
      {...props}
      leftContent={
        <nav className="hidden md:flex items-center space-x-2 text-sm text-muted-foreground">
          {breadcrumbs.map((crumb, index) => (
            <div key={index} className="flex items-center space-x-2">
              {index > 0 && <span>/</span>}
              {crumb.href ? (
                <a href={crumb.href} className="hover:text-foreground">
                  {crumb.label}
                </a>
              ) : (
                <span className="text-foreground">{crumb.label}</span>
              )}
            </div>
          ))}
        </nav>
      }
    />
  )
}
