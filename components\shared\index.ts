// ============================================================================
// SHARED COMPONENTS LIBRARY
// ============================================================================
// This file provides centralized exports for all shared components
// to eliminate redundancy between admin and student interfaces

// Layout components
export { LogoBranding } from "./logo-branding"
export { NavigationItem, NavigationList } from "./navigation-item"
export { UserProfileSection } from "./user-profile-section"
export { BaseSidebar, MobileSidebarContent } from "./base-sidebar"
export { BaseHeader, SearchHeader, BreadcrumbHeader } from "./base-header"
export {
  BaseLayout,
  AdminBaseLayout,
  StudentBaseLayout,
  SearchLayout,
  BreadcrumbLayout
} from "./base-layout"

// UI components
export * from "./ui"

// Types
export type { NavigationItemData } from "./navigation-item"

// Re-export commonly used types for convenience
export interface SharedLayoutProps {
  children: React.ReactNode
  navigationItems: NavigationItemData[]
  userData?: any
  userLoading?: boolean
  showUserStats?: boolean
  userProfileHref?: string
  userSettingsHref?: string
}

export interface BrandingConfig {
  logoHref: string
  logoIcon: any // LucideIcon
  logoTitle: string
  logoSubtitle: string
}

export interface UserData {
  name: string
  email: string
  image?: string
  level?: number
  totalPoints?: number
  role?: string
}

// Utility functions for creating navigation items
export const createNavigationItem = (
  title: string,
  href: string,
  icon: any,
  options?: {
    description?: string
    badge?: string
    children?: Array<{
      title: string
      href: string
      badge?: string
    }>
  }
): NavigationItemData => ({
  title,
  href,
  icon,
  ...options
})

// Common navigation item creators
export const createDashboardItem = (href: string, icon: any) => 
  createNavigationItem("Dashboard", href, icon, {
    description: "Overview and analytics"
  })

export const createSettingsItem = (href: string, icon: any) =>
  createNavigationItem("Settings", href, icon, {
    description: "System configuration"
  })

// Layout configuration presets
export const adminLayoutConfig = {
  roleBadge: "ADMIN",
  badgeVariant: "secondary" as const,
  showUserStats: false,
  enableSocket: true
}

export const studentLayoutConfig = {
  showUserStats: true,
  enableSocket: true,
  userProfileHref: "/student/profile",
  userSettingsHref: "/student/settings"
}

// Common styling presets
export const layoutStyles = {
  sidebar: {
    width: "w-64",
    adminWidth: "w-80",
    backgroundColor: "bg-background"
  },
  header: {
    backgroundColor: "bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
    sticky: true
  }
}
