import { NextRequest, NextResponse } from 'next/server'
import { createAPIHandler, APIResponse } from '@/lib/api-middleware'
import { userRoleSchema } from '@/lib/schemas'
import { auth } from '@/auth'
import { prisma } from '@/lib/prisma'

const updateRoleSchema = userRoleSchema

export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { role } = updateRoleSchema.parse(body)

    // Update user role in database
    await prisma.user.update({
      where: { id: session.user.id },
      data: { role }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error updating user role:', error)
    return NextResponse.json(
      { error: 'Failed to update role' },
      { status: 500 }
    )
  }
}
